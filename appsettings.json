{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=AmsWeb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "AmsWebMvc", "Audience": "AmsWebMvcUsers", "ExpirationHours": 12}, "EmailSettings": {"SmtpServer": "localhost", "SmtpPort": 587, "FromEmail": "<EMAIL>", "FromName": "AMS Notification System"}, "ApplicationSettings": {"TimeZone": "Australia/Melbourne", "EditWindowDays": 3, "AlertArchiveDays": 14, "InvitationExpiryDays": 2, "PasswordExpiryDays": 500}}