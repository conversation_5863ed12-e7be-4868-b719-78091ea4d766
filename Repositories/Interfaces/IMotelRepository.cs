using System.Linq.Expressions;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Models;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for Motel entity with domain-specific operations
/// </summary>
public interface IMotelRepository : IRepository<Motel>
{
    // Search and filtering
    Task<IEnumerable<Motel>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> SearchBySuburbAsync(string suburb, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> SearchByPostcodeAsync(string postcode, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetByRegionAsync(Region region, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetByStatusAsync(MotelStatus status, CancellationToken cancellationToken = default);
    
    // Advanced search with multiple criteria
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        Region? region = null,
        MotelStatus? status = null,
        List<int>? amenityIds = null,
        List<int>? serviceIds = null,
        List<int>? excludeMotelIds = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Amenity and service filtering
    Task<IEnumerable<Motel>> GetByAmenityAsync(int amenityId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetByServiceAsync(int serviceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetByAmenitiesAsync(List<int> amenityIds, bool requireAll = false, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetByServicesAsync(List<int> serviceIds, bool requireAll = false, CancellationToken cancellationToken = default);
    
    // Statistics and reporting
    Task<int> GetCountByRegionAsync(Region region, CancellationToken cancellationToken = default);
    Task<int> GetCountByStatusAsync(MotelStatus status, CancellationToken cancellationToken = default);
    Task<Dictionary<Region, int>> GetCountsByRegionAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<MotelStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Motel?> GetWithAmenitiesAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithServicesAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithAlertsAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithCommentsAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithCautionsAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithSuggestionsAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Business logic helpers
    Task<bool> HasActiveAlertsAsync(int motelId, CancellationToken cancellationToken = default);
    Task<bool> HasPendingCommentsAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetActiveAlertCountAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetPendingCommentCountAsync(int motelId, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> BulkUpdateStatusAsync(List<int> motelIds, MotelStatus status, CancellationToken cancellationToken = default);
    Task<int> BulkUpdateRegionAsync(List<int> motelIds, Region region, CancellationToken cancellationToken = default);
}
