using ams_web_mvc.Models;
using ams_web_mvc.Models.Identity;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Unit of Work pattern interface for managing transactions and coordinating repositories
/// </summary>
public interface IUnitOfWork : IDisposable
{
    // Core entity repositories
    IMotelRepository Motels { get; }
    IAlertRepository Alerts { get; }
    ICommentRepository Comments { get; }
    ICautionRepository Cautions { get; }
    ISuggestionRepository Suggestions { get; }
    
    // User and identity repositories
    IUserRepository Users { get; }
    IRoleRepository Roles { get; }
    
    // Supporting entity repositories
    IAmenityRepository Amenities { get; }
    IServiceRepository Services { get; }
    IOrganizationTypeRepository OrganizationTypes { get; }
    
    // History and audit repositories
    IAlertHistoryRepository AlertHistories { get; }
    ICommentHistoryRepository CommentHistories { get; }
    ICautionHistoryRepository CautionHistories { get; }
    ISuggestionHistoryRepository SuggestionHistories { get; }
    IMotelChangeLogRepository MotelChangeLogs { get; }
    
    // Notification repositories
    INotificationRepository Notifications { get; }
    INotificationPreferenceRepository NotificationPreferences { get; }
    
    // Many-to-many relationship repositories
    IMotelAmenityRepository MotelAmenities { get; }
    IMotelServiceRepository MotelServices { get; }
    
    // Generic repository access
    IRepository<T> Repository<T>() where T : Models.Base.BaseEntity;
    
    // Transaction management
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters);
    Task<int> ExecuteSqlRawAsync(string sql, CancellationToken cancellationToken, params object[] parameters);
}
