using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for Alert entity with workflow management
/// </summary>
public interface IAlertRepository : IRepository<Alert>
{
    // Status-based queries
    Task<IEnumerable<Alert>> GetByStatusAsync(AlertStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetPendingAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetApprovedAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetRejectedAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetArchivedAlertsAsync(CancellationToken cancellationToken = default);
    
    // Type-based queries
    Task<IEnumerable<Alert>> GetByTypeAsync(AlertType type, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByTypesAsync(List<AlertType> types, CancellationToken cancellationToken = default);
    
    // Motel-specific queries
    Task<IEnumerable<Alert>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetActiveByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetActiveCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    
    // User-specific queries
    Task<IEnumerable<Alert>> GetByReporterIdAsync(string reporterId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByApproverIdAsync(string approverId, CancellationToken cancellationToken = default);
    
    // Date-based queries
    Task<IEnumerable<Alert>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetRecentAlertsAsync(int days = 30, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetExpiredAlertsAsync(CancellationToken cancellationToken = default);
    
    // Priority-based queries
    Task<IEnumerable<Alert>> GetHighPriorityAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByPriorityAsync(AlertPriority priority, CancellationToken cancellationToken = default);
    
    // Workflow operations
    Task<Alert> ApproveAlertAsync(int alertId, string approverId, string? approvalNotes = null, CancellationToken cancellationToken = default);
    Task<Alert> RejectAlertAsync(int alertId, string approverId, string rejectionReason, CancellationToken cancellationToken = default);
    Task<Alert> ArchiveAlertAsync(int alertId, string archiverId, CancellationToken cancellationToken = default);
    Task<Alert> UpdateStatusAsync(int alertId, AlertStatus status, string userId, string? notes = null, CancellationToken cancellationToken = default);
    
    // Search and filtering
    Task<(IEnumerable<Alert> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        AlertStatus? status = null,
        AlertType? type = null,
        AlertPriority? priority = null,
        int? motelId = null,
        string? reporterId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Statistics and reporting
    Task<Dictionary<AlertStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<AlertType, int>> GetCountsByTypeAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<AlertPriority, int>> GetCountsByPriorityAsync(CancellationToken cancellationToken = default);
    Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Alert?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default);
    Task<Alert?> GetWithReporterAsync(int id, CancellationToken cancellationToken = default);
    Task<Alert?> GetWithApproverAsync(int id, CancellationToken cancellationToken = default);
    Task<Alert?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default);
    Task<Alert?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> BulkUpdateStatusAsync(List<int> alertIds, AlertStatus status, string userId, CancellationToken cancellationToken = default);
    Task<int> BulkArchiveAsync(List<int> alertIds, string archiverId, CancellationToken cancellationToken = default);
    Task<int> ArchiveExpiredAlertsAsync(CancellationToken cancellationToken = default);
}
