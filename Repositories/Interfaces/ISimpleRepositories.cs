using ams_web_mvc.Models;
using ams_web_mvc.Models.Identity;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for ApplicationRole entity
/// </summary>
public interface IRoleRepository : IRepository<ApplicationRole>
{
    Task<ApplicationRole?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> RoleExistsAsync(string name, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Amenity entity
/// </summary>
public interface IAmenityRepository : IRepository<Amenity>
{
    Task<IEnumerable<Amenity>> GetWithOptionsAsync(CancellationToken cancellationToken = default);
    Task<Amenity?> GetWithOptionsAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Amenity>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for AmenityOption entity
/// </summary>
public interface IAmenityOptionRepository : IRepository<AmenityOption>
{
    Task<IEnumerable<AmenityOption>> GetByAmenityIdAsync(int amenityId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Service entity
/// </summary>
public interface IServiceRepository : IRepository<Service>
{
    Task<IEnumerable<Service>> GetByOrganizationTypeIdAsync(int organizationTypeId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Service>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<Service?> GetWithOrganizationTypeAsync(int id, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for OrganizationType entity
/// </summary>
public interface IOrganizationTypeRepository : IRepository<OrganizationType>
{
    Task<IEnumerable<OrganizationType>> GetWithServicesAsync(CancellationToken cancellationToken = default);
    Task<OrganizationType?> GetWithServicesAsync(int id, CancellationToken cancellationToken = default);
    Task<OrganizationType?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for MotelAmenity entity
/// </summary>
public interface IMotelAmenityRepository : IRepository<MotelAmenity>
{
    Task<IEnumerable<MotelAmenity>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MotelAmenity>> GetByAmenityIdAsync(int amenityId, CancellationToken cancellationToken = default);
    Task<MotelAmenity?> GetByMotelAndAmenityAsync(int motelId, int amenityId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for MotelService entity
/// </summary>
public interface IMotelServiceRepository : IRepository<MotelService>
{
    Task<IEnumerable<MotelService>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MotelService>> GetByServiceIdAsync(int serviceId, CancellationToken cancellationToken = default);
    Task<MotelService?> GetByMotelAndServiceAsync(int motelId, int serviceId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Notification entity
/// </summary>
public interface INotificationRepository : IRepository<Notification>
{
    Task<IEnumerable<Notification>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Notification>> GetUnreadByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<int> GetUnreadCountByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<Notification> MarkAsReadAsync(int notificationId, CancellationToken cancellationToken = default);
    Task<int> MarkAllAsReadAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for NotificationPreference entity
/// </summary>
public interface INotificationPreferenceRepository : IRepository<NotificationPreference>
{
    Task<NotificationPreference?> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for AlertHistory entity
/// </summary>
public interface IAlertHistoryRepository : IRepository<AlertHistory>
{
    Task<IEnumerable<AlertHistory>> GetByAlertIdAsync(int alertId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for CommentHistory entity
/// </summary>
public interface ICommentHistoryRepository : IRepository<CommentHistory>
{
    Task<IEnumerable<CommentHistory>> GetByCommentIdAsync(int commentId, CancellationToken cancellationToken = default);
    Task<IEnumerable<CommentHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for CautionHistory entity
/// </summary>
public interface ICautionHistoryRepository : IRepository<CautionHistory>
{
    Task<IEnumerable<CautionHistory>> GetByCautionIdAsync(int cautionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<CautionHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for SuggestionHistory entity
/// </summary>
public interface ISuggestionHistoryRepository : IRepository<SuggestionHistory>
{
    Task<IEnumerable<SuggestionHistory>> GetBySuggestionIdAsync(int suggestionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<SuggestionHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for MotelChangeLog entity
/// </summary>
public interface IMotelChangeLogRepository : IRepository<MotelChangeLog>
{
    Task<IEnumerable<MotelChangeLog>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MotelChangeLog>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MotelChangeLog>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}
