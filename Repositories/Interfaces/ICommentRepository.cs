using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Models;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for Comment entity with moderation capabilities
/// </summary>
public interface ICommentRepository : IRepository<Comment>
{
    // Status-based queries
    Task<IEnumerable<Comment>> GetByStatusAsync(CommentStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetPendingCommentsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetApprovedCommentsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetRejectedCommentsAsync(CancellationToken cancellationToken = default);
    
    // Motel-specific queries
    Task<IEnumerable<Comment>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetApprovedByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetPendingByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetPendingCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    
    // User-specific queries
    Task<IEnumerable<Comment>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetByModeratorIdAsync(string moderatorId, CancellationToken cancellationToken = default);
    
    // Date-based queries
    Task<IEnumerable<Comment>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetRecentCommentsAsync(int days = 30, CancellationToken cancellationToken = default);
    
    // Moderation operations
    Task<Comment> ApproveCommentAsync(int commentId, string moderatorId, string? moderationNotes = null, CancellationToken cancellationToken = default);
    Task<Comment> RejectCommentAsync(int commentId, string moderatorId, string rejectionReason, CancellationToken cancellationToken = default);
    Task<Comment> UpdateStatusAsync(int commentId, CommentStatus status, string moderatorId, string? notes = null, CancellationToken cancellationToken = default);
    
    // Search and filtering
    Task<(IEnumerable<Comment> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        CommentStatus? status = null,
        int? motelId = null,
        string? authorId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Statistics and reporting
    Task<Dictionary<CommentStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default);
    Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetApprovedCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetRejectedCountAsync(CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Comment?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithModeratorAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> BulkUpdateStatusAsync(List<int> commentIds, CommentStatus status, string moderatorId, CancellationToken cancellationToken = default);
    Task<int> BulkApproveAsync(List<int> commentIds, string moderatorId, CancellationToken cancellationToken = default);
    Task<int> BulkRejectAsync(List<int> commentIds, string moderatorId, string rejectionReason, CancellationToken cancellationToken = default);
}
