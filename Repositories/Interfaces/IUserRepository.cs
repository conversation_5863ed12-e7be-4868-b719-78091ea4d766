using ams_web_mvc.Models.Identity;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for ApplicationUser entity
/// </summary>
public interface IUserRepository : IRepository<ApplicationUser>
{
    // Authentication and authorization
    Task<ApplicationUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<ApplicationUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<bool> EmailExistsAsync(string email, CancellationToken cancellationToken = default);
    Task<bool> UsernameExistsAsync(string username, CancellationToken cancellationToken = default);
    
    // Role-based queries
    Task<IEnumerable<ApplicationUser>> GetByRoleAsync(UserRole role, CancellationToken cancellationToken = default);
    Task<IEnumerable<ApplicationUser>> GetAdministratorsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ApplicationUser>> GetModeratorsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ApplicationUser>> GetContributorsAsync(CancellationToken cancellationToken = default);
    
    // Status-based queries
    Task<IEnumerable<ApplicationUser>> GetActiveUsersAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ApplicationUser>> GetInactiveUsersAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ApplicationUser>> GetLockedUsersAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ApplicationUser>> GetUsersWithExpiredPasswordsAsync(CancellationToken cancellationToken = default);
    
    // Search and filtering
    Task<(IEnumerable<ApplicationUser> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        UserRole? role = null,
        bool? isActive = null,
        bool? isLocked = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Statistics and reporting
    Task<Dictionary<UserRole, int>> GetCountsByRoleAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetInactiveCountAsync(CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<ApplicationUser?> GetWithRolesAsync(string id, CancellationToken cancellationToken = default);
    Task<ApplicationUser?> GetWithAlertsAsync(string id, CancellationToken cancellationToken = default);
    Task<ApplicationUser?> GetWithCommentsAsync(string id, CancellationToken cancellationToken = default);
    Task<ApplicationUser?> GetWithAllRelatedDataAsync(string id, CancellationToken cancellationToken = default);
    
    // User management operations
    Task<ApplicationUser> ActivateUserAsync(string userId, CancellationToken cancellationToken = default);
    Task<ApplicationUser> DeactivateUserAsync(string userId, CancellationToken cancellationToken = default);
    Task<ApplicationUser> LockUserAsync(string userId, CancellationToken cancellationToken = default);
    Task<ApplicationUser> UnlockUserAsync(string userId, CancellationToken cancellationToken = default);
    Task<ApplicationUser> UpdateLastLoginAsync(string userId, CancellationToken cancellationToken = default);
}
