using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for Suggestion entity
/// </summary>
public interface ISuggestionRepository : IRepository<Suggestion>
{
    // Status-based queries
    Task<IEnumerable<Suggestion>> GetByStatusAsync(SuggestionStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Suggestion>> GetPendingSuggestionsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Suggestion>> GetApprovedSuggestionsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Suggestion>> GetRejectedSuggestionsAsync(CancellationToken cancellationToken = default);
    
    // Motel-specific queries
    Task<IEnumerable<Suggestion>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    
    // User-specific queries
    Task<IEnumerable<Suggestion>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Suggestion>> GetByApproverIdAsync(string approverId, CancellationToken cancellationToken = default);
    
    // Date-based queries
    Task<IEnumerable<Suggestion>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Suggestion>> GetRecentSuggestionsAsync(int days = 30, CancellationToken cancellationToken = default);
    
    // Workflow operations
    Task<Suggestion> ApproveSuggestionAsync(int suggestionId, string approverId, string? approvalNotes = null, CancellationToken cancellationToken = default);
    Task<Suggestion> RejectSuggestionAsync(int suggestionId, string approverId, string rejectionReason, CancellationToken cancellationToken = default);
    Task<Suggestion> UpdateStatusAsync(int suggestionId, SuggestionStatus status, string userId, string? notes = null, CancellationToken cancellationToken = default);
    
    // Search and filtering
    Task<(IEnumerable<Suggestion> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        SuggestionStatus? status = null,
        int? motelId = null,
        string? authorId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Statistics and reporting
    Task<Dictionary<SuggestionStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default);
    Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Suggestion?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default);
    Task<Suggestion?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default);
    Task<Suggestion?> GetWithApproverAsync(int id, CancellationToken cancellationToken = default);
    Task<Suggestion?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default);
    Task<Suggestion?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> BulkUpdateStatusAsync(List<int> suggestionIds, SuggestionStatus status, string userId, CancellationToken cancellationToken = default);
}
