using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Models;

namespace ams_web_mvc.Repositories.Interfaces;

/// <summary>
/// Repository interface for Caution entity
/// </summary>
public interface ICautionRepository : IRepository<Caution>
{
    // Motel-specific queries
    Task<IEnumerable<Caution>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    
    // User-specific queries
    Task<IEnumerable<Caution>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default);
    
    // Date-based queries
    Task<IEnumerable<Caution>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Caution>> GetRecentCautionsAsync(int days = 30, CancellationToken cancellationToken = default);
    
    // Search and filtering
    Task<(IEnumerable<Caution> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        int? motelId = null,
        string? authorId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Caution?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default);
    Task<Caution?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default);
    Task<Caution?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default);
    Task<Caution?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default);
}
