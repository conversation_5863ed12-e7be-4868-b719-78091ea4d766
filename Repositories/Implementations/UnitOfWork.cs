using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Repositories.Interfaces;

namespace ams_web_mvc.Repositories.Implementations;

/// <summary>
/// Unit of Work implementation for managing transactions and coordinating repositories
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _context;
    private IDbContextTransaction? _transaction;
    private readonly Dictionary<Type, object> _repositories = new();
    private bool _disposed = false;

    // Repository properties - lazy loaded
    private IMotelRepository? _motels;
    private IAlertRepository? _alerts;
    private ICommentRepository? _comments;
    private ICautionRepository? _cautions;
    private ISuggestionRepository? _suggestions;
    private IUserRepository? _users;
    private IRoleRepository? _roles;
    private IAmenityRepository? _amenities;
    private IServiceRepository? _services;
    private IOrganizationTypeRepository? _organizationTypes;
    private IAlertHistoryRepository? _alertHistories;
    private ICommentHistoryRepository? _commentHistories;
    private ICautionHistoryRepository? _cautionHistories;
    private ISuggestionHistoryRepository? _suggestionHistories;
    private IMotelChangeLogRepository? _motelChangeLogs;
    private INotificationRepository? _notifications;
    private INotificationPreferenceRepository? _notificationPreferences;
    private IMotelAmenityRepository? _motelAmenities;
    private IMotelServiceRepository? _motelServices;

    public UnitOfWork(ApplicationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    #region Repository Properties

    public IMotelRepository Motels => _motels ??= new MotelRepository(_context);
    public IAlertRepository Alerts => _alerts ??= new AlertRepository(_context);
    public ICommentRepository Comments => _comments ??= new CommentRepository(_context);
    public ICautionRepository Cautions => _cautions ??= new CautionRepository(_context);
    public ISuggestionRepository Suggestions => _suggestions ??= new SuggestionRepository(_context);
    public IUserRepository Users => _users ??= new UserRepository(_context);
    public IRoleRepository Roles => _roles ??= new RoleRepository(_context);
    public IAmenityRepository Amenities => _amenities ??= new AmenityRepository(_context);
    public IServiceRepository Services => _services ??= new ServiceRepository(_context);
    public IOrganizationTypeRepository OrganizationTypes => _organizationTypes ??= new OrganizationTypeRepository(_context);
    public IAlertHistoryRepository AlertHistories => _alertHistories ??= new AlertHistoryRepository(_context);
    public ICommentHistoryRepository CommentHistories => _commentHistories ??= new CommentHistoryRepository(_context);
    public ICautionHistoryRepository CautionHistories => _cautionHistories ??= new CautionHistoryRepository(_context);
    public ISuggestionHistoryRepository SuggestionHistories => _suggestionHistories ??= new SuggestionHistoryRepository(_context);
    public IMotelChangeLogRepository MotelChangeLogs => _motelChangeLogs ??= new MotelChangeLogRepository(_context);
    public INotificationRepository Notifications => _notifications ??= new NotificationRepository(_context);
    public INotificationPreferenceRepository NotificationPreferences => _notificationPreferences ??= new NotificationPreferenceRepository(_context);
    public IMotelAmenityRepository MotelAmenities => _motelAmenities ??= new MotelAmenityRepository(_context);
    public IMotelServiceRepository MotelServices => _motelServices ??= new MotelServiceRepository(_context);

    #endregion

    #region Generic Repository Access

    public IRepository<T> Repository<T>() where T : BaseEntity
    {
        var type = typeof(T);
        
        if (_repositories.ContainsKey(type))
        {
            return (IRepository<T>)_repositories[type];
        }

        var repository = new Repository<T>(_context);
        _repositories[type] = repository;
        return repository;
    }

    #endregion

    #region Transaction Management

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateConcurrencyException ex)
        {
            // Handle concurrency conflicts
            throw new InvalidOperationException("The record you attempted to edit was modified by another user after you got the original value.", ex);
        }
        catch (DbUpdateException ex)
        {
            // Handle database update exceptions
            throw new InvalidOperationException("An error occurred while saving to the database.", ex);
        }
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            throw new InvalidOperationException("A transaction is already in progress.");
        }

        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("No transaction is in progress.");
        }

        try
        {
            await _transaction.CommitAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("No transaction is in progress.");
        }

        try
        {
            await _transaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    #endregion

    #region Bulk Operations

    public async Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters)
    {
        return await _context.Database.ExecuteSqlRawAsync(sql, parameters);
    }

    public async Task<int> ExecuteSqlRawAsync(string sql, CancellationToken cancellationToken, params object[] parameters)
    {
        return await _context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
    }

    #endregion

    #region Dispose Pattern

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction?.Dispose();
            _context.Dispose();
            _disposed = true;
        }
    }

    #endregion
}
