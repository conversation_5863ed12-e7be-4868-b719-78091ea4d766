using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Models;
using ams_web_mvc.Repositories.Interfaces;

namespace ams_web_mvc.Repositories.Implementations;

#region Many-to-Many Repositories

public class MotelAmenityRepository : Repository<MotelAmenity>, IMotelAmenityRepository
{
    public MotelAmenityRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<MotelAmenity>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ma => ma.MotelId == motelId).Include(ma => ma.Amenity).Include(ma => ma.AmenityOption).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MotelAmenity>> GetByAmenityIdAsync(int amenityId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ma => ma.AmenityId == amenityId).Include(ma => ma.Motel).ToListAsync(cancellationToken);
    }

    public async Task<MotelAmenity?> GetByMotelAndAmenityAsync(int motelId, int amenityId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(ma => ma.MotelId == motelId && ma.AmenityId == amenityId, cancellationToken);
    }
}

public class MotelServiceRepository : Repository<MotelService>, IMotelServiceRepository
{
    public MotelServiceRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<MotelService>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ms => ms.MotelId == motelId).Include(ms => ms.Service).ThenInclude(s => s.OrganizationType).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MotelService>> GetByServiceIdAsync(int serviceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ms => ms.ServiceId == serviceId).Include(ms => ms.Motel).ToListAsync(cancellationToken);
    }

    public async Task<MotelService?> GetByMotelAndServiceAsync(int motelId, int serviceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(ms => ms.MotelId == motelId && ms.ServiceId == serviceId, cancellationToken);
    }
}

#endregion

#region Notification Repositories

public class NotificationRepository : Repository<Notification>, INotificationRepository
{
    public NotificationRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<Notification>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(n => n.UserId == userId).OrderByDescending(n => n.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Notification>> GetUnreadByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(n => n.UserId == userId && !n.IsRead).OrderByDescending(n => n.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<int> GetUnreadCountByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(n => n.UserId == userId && !n.IsRead, cancellationToken);
    }

    public async Task<Notification> MarkAsReadAsync(int notificationId, CancellationToken cancellationToken = default)
    {
        var notification = await GetByIdAsync(notificationId, cancellationToken);
        if (notification == null) throw new ArgumentException($"Notification with ID {notificationId} not found.");
        
        notification.IsRead = true;
        notification.ReadAt = DateTime.UtcNow;
        notification.UpdatedAt = DateTime.UtcNow;
        
        _dbSet.Update(notification);
        return notification;
    }

    public async Task<int> MarkAllAsReadAsync(string userId, CancellationToken cancellationToken = default)
    {
        var notifications = await _dbSet.Where(n => n.UserId == userId && !n.IsRead).ToListAsync(cancellationToken);
        var now = DateTime.UtcNow;
        
        foreach (var notification in notifications)
        {
            notification.IsRead = true;
            notification.ReadAt = now;
            notification.UpdatedAt = now;
        }
        
        _dbSet.UpdateRange(notifications);
        return notifications.Count;
    }
}

public class NotificationPreferenceRepository : Repository<NotificationPreference>, INotificationPreferenceRepository
{
    public NotificationPreferenceRepository(ApplicationDbContext context) : base(context) { }

    public async Task<NotificationPreference?> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(np => np.UserId == userId, cancellationToken);
    }
}

#endregion

#region History Repositories

public class AlertHistoryRepository : Repository<AlertHistory>, IAlertHistoryRepository
{
    public AlertHistoryRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<AlertHistory>> GetByAlertIdAsync(int alertId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ah => ah.AlertId == alertId).Include(ah => ah.User).OrderByDescending(ah => ah.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<AlertHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ah => ah.UserId == userId).Include(ah => ah.Alert).OrderByDescending(ah => ah.CreatedAt).ToListAsync(cancellationToken);
    }
}

public class CommentHistoryRepository : Repository<CommentHistory>, ICommentHistoryRepository
{
    public CommentHistoryRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<CommentHistory>> GetByCommentIdAsync(int commentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ch => ch.CommentId == commentId).Include(ch => ch.User).OrderByDescending(ch => ch.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CommentHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ch => ch.UserId == userId).Include(ch => ch.Comment).OrderByDescending(ch => ch.CreatedAt).ToListAsync(cancellationToken);
    }
}

public class CautionHistoryRepository : Repository<CautionHistory>, ICautionHistoryRepository
{
    public CautionHistoryRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<CautionHistory>> GetByCautionIdAsync(int cautionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ch => ch.CautionId == cautionId).Include(ch => ch.User).OrderByDescending(ch => ch.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CautionHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ch => ch.UserId == userId).Include(ch => ch.Caution).OrderByDescending(ch => ch.CreatedAt).ToListAsync(cancellationToken);
    }
}

public class SuggestionHistoryRepository : Repository<SuggestionHistory>, ISuggestionHistoryRepository
{
    public SuggestionHistoryRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<SuggestionHistory>> GetBySuggestionIdAsync(int suggestionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(sh => sh.SuggestionId == suggestionId).Include(sh => sh.User).OrderByDescending(sh => sh.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SuggestionHistory>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(sh => sh.UserId == userId).Include(sh => sh.Suggestion).OrderByDescending(sh => sh.CreatedAt).ToListAsync(cancellationToken);
    }
}

public class MotelChangeLogRepository : Repository<MotelChangeLog>, IMotelChangeLogRepository
{
    public MotelChangeLogRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<MotelChangeLog>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(mcl => mcl.MotelId == motelId).Include(mcl => mcl.User).OrderByDescending(mcl => mcl.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MotelChangeLog>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(mcl => mcl.UserId == userId).Include(mcl => mcl.Motel).OrderByDescending(mcl => mcl.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MotelChangeLog>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(mcl => mcl.CreatedAt >= startDate && mcl.CreatedAt <= endDate)
            .Include(mcl => mcl.User).Include(mcl => mcl.Motel).OrderByDescending(mcl => mcl.CreatedAt).ToListAsync(cancellationToken);
    }
}

#endregion
