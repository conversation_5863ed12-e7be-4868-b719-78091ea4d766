using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Models;
using ams_web_mvc.Repositories.Interfaces;

namespace ams_web_mvc.Repositories.Implementations;

/// <summary>
/// Repository implementation for Alert entity with workflow management
/// </summary>
public class AlertRepository : Repository<Alert>, IAlertRepository
{
    public AlertRepository(ApplicationDbContext context) : base(context)
    {
    }

    #region Status-based Queries

    public async Task<IEnumerable<Alert>> GetByStatusAsync(AlertStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.Status == status)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetPendingAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(AlertStatus.Pending, cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetApprovedAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(AlertStatus.Approved, cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetRejectedAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(AlertStatus.Rejected, cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetArchivedAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(AlertStatus.Archived, cancellationToken);
    }

    #endregion

    #region Type-based Queries

    public async Task<IEnumerable<Alert>> GetByTypeAsync(AlertType type, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.Type == type)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetByTypesAsync(List<AlertType> types, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => types.Contains(a.Type))
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region Motel-specific Queries

    public async Task<IEnumerable<Alert>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.MotelId == motelId)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetActiveByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.MotelId == motelId && a.Status == AlertStatus.Approved)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetActiveCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .CountAsync(a => a.MotelId == motelId && a.Status == AlertStatus.Approved, cancellationToken);
    }

    #endregion

    #region User-specific Queries

    public async Task<IEnumerable<Alert>> GetByReporterIdAsync(string reporterId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.ReporterId == reporterId)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetByApproverIdAsync(string approverId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.ApproverId == approverId)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region Date-based Queries

    public async Task<IEnumerable<Alert>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetRecentAlertsAsync(int days = 30, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        return await _dbSet
            .Where(a => a.CreatedAt >= cutoffDate)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetExpiredAlertsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(a => a.ExpiryDate.HasValue && a.ExpiryDate < now && a.Status == AlertStatus.Approved)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region Priority-based Queries

    public async Task<IEnumerable<Alert>> GetHighPriorityAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByPriorityAsync(AlertPriority.High, cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetByPriorityAsync(AlertPriority priority, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.Priority == priority)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region Workflow Operations

    public async Task<Alert> ApproveAlertAsync(int alertId, string approverId, string? approvalNotes = null, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(alertId, AlertStatus.Approved, approverId, approvalNotes, cancellationToken);
    }

    public async Task<Alert> RejectAlertAsync(int alertId, string approverId, string rejectionReason, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(alertId, AlertStatus.Rejected, approverId, rejectionReason, cancellationToken);
    }

    public async Task<Alert> ArchiveAlertAsync(int alertId, string archiverId, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(alertId, AlertStatus.Archived, archiverId, "Archived", cancellationToken);
    }

    public async Task<Alert> UpdateStatusAsync(int alertId, AlertStatus status, string userId, string? notes = null, CancellationToken cancellationToken = default)
    {
        var alert = await GetByIdAsync(alertId, cancellationToken);
        if (alert == null)
            throw new ArgumentException($"Alert with ID {alertId} not found.");

        alert.Status = status;
        alert.UpdatedAt = DateTime.UtcNow;

        if (status == AlertStatus.Approved || status == AlertStatus.Rejected)
        {
            alert.ApproverId = userId;
            alert.ApprovedAt = DateTime.UtcNow;
            alert.ApprovalNotes = notes;
        }

        _dbSet.Update(alert);
        return alert;
    }

    #endregion

    #region Search and Filtering

    public async Task<(IEnumerable<Alert> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        AlertStatus? status = null,
        AlertType? type = null,
        AlertPriority? priority = null,
        int? motelId = null,
        string? reporterId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(a => a.Title.Contains(searchTerm) || a.Description.Contains(searchTerm));
        }

        if (status.HasValue)
        {
            query = query.Where(a => a.Status == status.Value);
        }

        if (type.HasValue)
        {
            query = query.Where(a => a.Type == type.Value);
        }

        if (priority.HasValue)
        {
            query = query.Where(a => a.Priority == priority.Value);
        }

        if (motelId.HasValue)
        {
            query = query.Where(a => a.MotelId == motelId.Value);
        }

        if (!string.IsNullOrWhiteSpace(reporterId))
        {
            query = query.Where(a => a.ReporterId == reporterId);
        }

        if (startDate.HasValue)
        {
            query = query.Where(a => a.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(a => a.CreatedAt <= endDate.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderByDescending(a => a.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    #endregion

    #region Statistics and Reporting

    public async Task<Dictionary<AlertStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(a => a.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<AlertType, int>> GetCountsByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(a => a.Type)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<AlertPriority, int>> GetCountsByPriorityAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(a => a.Priority)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(a => a.Status == AlertStatus.Pending, cancellationToken);
    }

    public async Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(a => a.Status == AlertStatus.Approved, cancellationToken);
    }

    #endregion

    #region Related Data Loading

    public async Task<Alert?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Motel)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<Alert?> GetWithReporterAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Reporter)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<Alert?> GetWithApproverAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Approver)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<Alert?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.AlertHistories)
                .ThenInclude(h => h.User)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<Alert?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Motel)
            .Include(a => a.Reporter)
            .Include(a => a.Approver)
            .Include(a => a.AlertHistories)
                .ThenInclude(h => h.User)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    #endregion

    #region Bulk Operations

    public async Task<int> BulkUpdateStatusAsync(List<int> alertIds, AlertStatus status, string userId, CancellationToken cancellationToken = default)
    {
        var alerts = await _dbSet
            .Where(a => alertIds.Contains(a.Id))
            .ToListAsync(cancellationToken);

        var now = DateTime.UtcNow;
        foreach (var alert in alerts)
        {
            alert.Status = status;
            alert.UpdatedAt = now;
            if (status == AlertStatus.Approved || status == AlertStatus.Rejected)
            {
                alert.ApproverId = userId;
                alert.ApprovedAt = now;
            }
        }

        _dbSet.UpdateRange(alerts);
        return alerts.Count;
    }

    public async Task<int> BulkArchiveAsync(List<int> alertIds, string archiverId, CancellationToken cancellationToken = default)
    {
        return await BulkUpdateStatusAsync(alertIds, AlertStatus.Archived, archiverId, cancellationToken);
    }

    public async Task<int> ArchiveExpiredAlertsAsync(CancellationToken cancellationToken = default)
    {
        var expiredAlerts = await GetExpiredAlertsAsync(cancellationToken);
        var alertIds = expiredAlerts.Select(a => a.Id).ToList();

        if (alertIds.Any())
        {
            return await BulkUpdateStatusAsync(alertIds, AlertStatus.Archived, "System", cancellationToken);
        }

        return 0;
    }

    #endregion
}
