using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Identity;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;

namespace ams_web_mvc.Repositories.Implementations;

#region User Repository

public class UserRepository : Repository<ApplicationUser>, IUserRepository
{
    public UserRepository(ApplicationDbContext context) : base(context) { }

    public async Task<ApplicationUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
    }

    public async Task<ApplicationUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(u => u.UserName == username, cancellationToken);
    }

    public async Task<bool> EmailExistsAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(u => u.Email == email, cancellationToken);
    }

    public async Task<bool> UsernameExistsAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(u => u.UserName == username, cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetByRoleAsync(UserRole role, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(u => u.Role == role).OrderBy(u => u.FirstName).ThenBy(u => u.LastName).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetAdministratorsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByRoleAsync(UserRole.Administrator, cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetModeratorsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByRoleAsync(UserRole.Moderator, cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetContributorsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByRoleAsync(UserRole.Contributor, cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetActiveUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(u => u.IsActive).OrderBy(u => u.FirstName).ThenBy(u => u.LastName).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetInactiveUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(u => !u.IsActive).OrderBy(u => u.FirstName).ThenBy(u => u.LastName).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetLockedUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(u => u.LockoutEnd.HasValue && u.LockoutEnd > DateTimeOffset.UtcNow).OrderBy(u => u.FirstName).ThenBy(u => u.LastName).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ApplicationUser>> GetUsersWithExpiredPasswordsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(u => u.PasswordExpiryDate.HasValue && u.PasswordExpiryDate < DateTime.UtcNow).OrderBy(u => u.FirstName).ThenBy(u => u.LastName).ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<ApplicationUser> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null, UserRole? role = null, bool? isActive = null, bool? isLocked = null,
        DateTime? startDate = null, DateTime? endDate = null, int pageNumber = 1, int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
            query = query.Where(u => u.FirstName.Contains(searchTerm) || u.LastName.Contains(searchTerm) || u.Email.Contains(searchTerm));
        if (role.HasValue)
            query = query.Where(u => u.Role == role.Value);
        if (isActive.HasValue)
            query = query.Where(u => u.IsActive == isActive.Value);
        if (isLocked.HasValue)
        {
            if (isLocked.Value)
                query = query.Where(u => u.LockoutEnd.HasValue && u.LockoutEnd > DateTimeOffset.UtcNow);
            else
                query = query.Where(u => !u.LockoutEnd.HasValue || u.LockoutEnd <= DateTimeOffset.UtcNow);
        }
        if (startDate.HasValue)
            query = query.Where(u => u.CreatedAt >= startDate.Value);
        if (endDate.HasValue)
            query = query.Where(u => u.CreatedAt <= endDate.Value);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query.OrderBy(u => u.FirstName).ThenBy(u => u.LastName).Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Dictionary<UserRole, int>> GetCountsByRoleAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.GroupBy(u => u.Role).ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(u => u.IsActive, cancellationToken);
    }

    public async Task<int> GetInactiveCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(u => !u.IsActive, cancellationToken);
    }

    public async Task<ApplicationUser?> GetWithRolesAsync(string id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    public async Task<ApplicationUser?> GetWithAlertsAsync(string id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(u => u.ReportedAlerts).FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    public async Task<ApplicationUser?> GetWithCommentsAsync(string id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(u => u.Comments).FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    public async Task<ApplicationUser?> GetWithAllRelatedDataAsync(string id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(u => u.ReportedAlerts).Include(u => u.Comments).FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    public async Task<ApplicationUser> ActivateUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        var user = await GetByIdAsync(int.Parse(userId), cancellationToken);
        if (user == null) throw new ArgumentException($"User with ID {userId} not found.");
        user.IsActive = true;
        user.UpdatedAt = DateTime.UtcNow;
        _dbSet.Update(user);
        return user;
    }

    public async Task<ApplicationUser> DeactivateUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        var user = await GetByIdAsync(int.Parse(userId), cancellationToken);
        if (user == null) throw new ArgumentException($"User with ID {userId} not found.");
        user.IsActive = false;
        user.UpdatedAt = DateTime.UtcNow;
        _dbSet.Update(user);
        return user;
    }

    public async Task<ApplicationUser> LockUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        var user = await GetByIdAsync(int.Parse(userId), cancellationToken);
        if (user == null) throw new ArgumentException($"User with ID {userId} not found.");
        user.LockoutEnd = DateTimeOffset.UtcNow.AddYears(100);
        user.UpdatedAt = DateTime.UtcNow;
        _dbSet.Update(user);
        return user;
    }

    public async Task<ApplicationUser> UnlockUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        var user = await GetByIdAsync(int.Parse(userId), cancellationToken);
        if (user == null) throw new ArgumentException($"User with ID {userId} not found.");
        user.LockoutEnd = null;
        user.UpdatedAt = DateTime.UtcNow;
        _dbSet.Update(user);
        return user;
    }

    public async Task<ApplicationUser> UpdateLastLoginAsync(string userId, CancellationToken cancellationToken = default)
    {
        var user = await GetByIdAsync(int.Parse(userId), cancellationToken);
        if (user == null) throw new ArgumentException($"User with ID {userId} not found.");
        user.LastLoginAt = DateTime.UtcNow;
        user.UpdatedAt = DateTime.UtcNow;
        _dbSet.Update(user);
        return user;
    }
}

#endregion

#region Role Repository

public class RoleRepository : Repository<ApplicationRole>, IRoleRepository
{
    public RoleRepository(ApplicationDbContext context) : base(context) { }

    public async Task<ApplicationRole?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(r => r.Name == name, cancellationToken);
    }

    public async Task<bool> RoleExistsAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(r => r.Name == name, cancellationToken);
    }
}

#endregion

#region Amenity Repository

public class AmenityRepository : Repository<Amenity>, IAmenityRepository
{
    public AmenityRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<Amenity>> GetWithOptionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(a => a.AmenityOptions).OrderBy(a => a.Name).ToListAsync(cancellationToken);
    }

    public async Task<Amenity?> GetWithOptionsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(a => a.AmenityOptions).FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<Amenity>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(a => a.Name.Contains(searchTerm)).OrderBy(a => a.Name).ToListAsync(cancellationToken);
    }
}

#endregion

#region AmenityOption Repository

public class AmenityOptionRepository : Repository<AmenityOption>, IAmenityOptionRepository
{
    public AmenityOptionRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<AmenityOption>> GetByAmenityIdAsync(int amenityId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(ao => ao.AmenityId == amenityId).OrderBy(ao => ao.Name).ToListAsync(cancellationToken);
    }
}

#endregion

#region Service Repository

public class ServiceRepository : Repository<Service>, IServiceRepository
{
    public ServiceRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<Service>> GetByOrganizationTypeIdAsync(int organizationTypeId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.OrganizationTypeId == organizationTypeId).OrderBy(s => s.Name).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Service>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.Name.Contains(searchTerm)).OrderBy(s => s.Name).ToListAsync(cancellationToken);
    }

    public async Task<Service?> GetWithOrganizationTypeAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(s => s.OrganizationType).FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }
}

#endregion

#region OrganizationType Repository

public class OrganizationTypeRepository : Repository<OrganizationType>, IOrganizationTypeRepository
{
    public OrganizationTypeRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<OrganizationType>> GetWithServicesAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(ot => ot.Services).OrderBy(ot => ot.Name).ToListAsync(cancellationToken);
    }

    public async Task<OrganizationType?> GetWithServicesAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(ot => ot.Services).FirstOrDefaultAsync(ot => ot.Id == id, cancellationToken);
    }

    public async Task<OrganizationType?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(ot => ot.Name == name, cancellationToken);
    }
}

#endregion
