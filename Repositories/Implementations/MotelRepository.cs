using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;

namespace ams_web_mvc.Repositories.Implementations;

/// <summary>
/// Repository implementation for Motel entity with domain-specific operations
/// </summary>
public class MotelRepository : Repository<Motel>, IMotelRepository
{
    public MotelRepository(ApplicationDbContext context) : base(context)
    {
    }

    #region Search and Filtering

    public async Task<IEnumerable<Motel>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return Enumerable.Empty<Motel>();

        return await _dbSet
            .Where(m => m.Name.Contains(searchTerm))
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> SearchBySuburbAsync(string suburb, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(suburb))
            return Enumerable.Empty<Motel>();

        return await _dbSet
            .Where(m => m.Suburb.Contains(suburb))
            .OrderBy(m => m.Suburb)
            .ThenBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> SearchByPostcodeAsync(string postcode, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(postcode))
            return Enumerable.Empty<Motel>();

        return await _dbSet
            .Where(m => m.Postcode == postcode)
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> GetByRegionAsync(Region region, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(m => m.Region == region)
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> GetByStatusAsync(MotelStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(m => m.Status == status)
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region Advanced Search

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        Region? region = null,
        MotelStatus? status = null,
        List<int>? amenityIds = null,
        List<int>? serviceIds = null,
        List<int>? excludeMotelIds = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        // Apply search term filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(m => 
                m.Name.Contains(searchTerm) || 
                m.Suburb.Contains(searchTerm) || 
                m.Postcode.Contains(searchTerm) ||
                m.Address.Contains(searchTerm));
        }

        // Apply region filter
        if (region.HasValue)
        {
            query = query.Where(m => m.Region == region.Value);
        }

        // Apply status filter
        if (status.HasValue)
        {
            query = query.Where(m => m.Status == status.Value);
        }

        // Apply amenity filter
        if (amenityIds != null && amenityIds.Any())
        {
            query = query.Where(m => m.MotelAmenities.Any(ma => amenityIds.Contains(ma.AmenityId)));
        }

        // Apply service filter
        if (serviceIds != null && serviceIds.Any())
        {
            query = query.Where(m => m.MotelServices.Any(ms => serviceIds.Contains(ms.ServiceId)));
        }

        // Apply exclusion filter
        if (excludeMotelIds != null && excludeMotelIds.Any())
        {
            query = query.Where(m => !excludeMotelIds.Contains(m.Id));
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var items = await query
            .OrderBy(m => m.Name)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    #endregion

    #region Amenity and Service Filtering

    public async Task<IEnumerable<Motel>> GetByAmenityAsync(int amenityId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(m => m.MotelAmenities.Any(ma => ma.AmenityId == amenityId))
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> GetByServiceAsync(int serviceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(m => m.MotelServices.Any(ms => ms.ServiceId == serviceId))
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> GetByAmenitiesAsync(List<int> amenityIds, bool requireAll = false, CancellationToken cancellationToken = default)
    {
        if (!amenityIds.Any())
            return Enumerable.Empty<Motel>();

        var query = _dbSet.AsQueryable();

        if (requireAll)
        {
            // Motel must have ALL specified amenities
            foreach (var amenityId in amenityIds)
            {
                query = query.Where(m => m.MotelAmenities.Any(ma => ma.AmenityId == amenityId));
            }
        }
        else
        {
            // Motel must have ANY of the specified amenities
            query = query.Where(m => m.MotelAmenities.Any(ma => amenityIds.Contains(ma.AmenityId)));
        }

        return await query
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Motel>> GetByServicesAsync(List<int> serviceIds, bool requireAll = false, CancellationToken cancellationToken = default)
    {
        if (!serviceIds.Any())
            return Enumerable.Empty<Motel>();

        var query = _dbSet.AsQueryable();

        if (requireAll)
        {
            // Motel must have ALL specified services
            foreach (var serviceId in serviceIds)
            {
                query = query.Where(m => m.MotelServices.Any(ms => ms.ServiceId == serviceId));
            }
        }
        else
        {
            // Motel must have ANY of the specified services
            query = query.Where(m => m.MotelServices.Any(ms => serviceIds.Contains(ms.ServiceId)));
        }

        return await query
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region Statistics and Reporting

    public async Task<int> GetCountByRegionAsync(Region region, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(m => m.Region == region, cancellationToken);
    }

    public async Task<int> GetCountByStatusAsync(MotelStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(m => m.Status == status, cancellationToken);
    }

    public async Task<Dictionary<Region, int>> GetCountsByRegionAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(m => m.Region)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<MotelStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(m => m.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    #endregion

    #region Related Data Loading

    public async Task<Motel?> GetWithAmenitiesAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.MotelAmenities)
                .ThenInclude(ma => ma.Amenity)
            .Include(m => m.MotelAmenities)
                .ThenInclude(ma => ma.AmenityOption)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Motel?> GetWithServicesAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.MotelServices)
                .ThenInclude(ms => ms.Service)
                    .ThenInclude(s => s.OrganizationType)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Motel?> GetWithAlertsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.Alerts.Where(a => a.Status != AlertStatus.Archived))
                .ThenInclude(a => a.Reporter)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Motel?> GetWithCommentsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.Comments.Where(c => c.Status == CommentStatus.Approved))
                .ThenInclude(c => c.Author)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Motel?> GetWithCautionsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.Cautions)
                .ThenInclude(c => c.Author)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Motel?> GetWithSuggestionsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.Suggestions.Where(s => s.Status != SuggestionStatus.Rejected))
                .ThenInclude(s => s.Author)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Motel?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(m => m.MotelAmenities)
                .ThenInclude(ma => ma.Amenity)
            .Include(m => m.MotelAmenities)
                .ThenInclude(ma => ma.AmenityOption)
            .Include(m => m.MotelServices)
                .ThenInclude(ms => ms.Service)
                    .ThenInclude(s => s.OrganizationType)
            .Include(m => m.Alerts.Where(a => a.Status != AlertStatus.Archived))
                .ThenInclude(a => a.Reporter)
            .Include(m => m.Comments.Where(c => c.Status == CommentStatus.Approved))
                .ThenInclude(c => c.Author)
            .Include(m => m.Cautions)
                .ThenInclude(c => c.Author)
            .Include(m => m.Suggestions.Where(s => s.Status != SuggestionStatus.Rejected))
                .ThenInclude(s => s.Author)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    #endregion

    #region Business Logic Helpers

    public async Task<bool> HasActiveAlertsAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .AnyAsync(a => a.MotelId == motelId && a.Status == AlertStatus.Approved, cancellationToken);
    }

    public async Task<bool> HasPendingCommentsAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _context.Comments
            .AnyAsync(c => c.MotelId == motelId && c.Status == CommentStatus.Pending, cancellationToken);
    }

    public async Task<int> GetActiveAlertCountAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .CountAsync(a => a.MotelId == motelId && a.Status == AlertStatus.Approved, cancellationToken);
    }

    public async Task<int> GetPendingCommentCountAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _context.Comments
            .CountAsync(c => c.MotelId == motelId && c.Status == CommentStatus.Pending, cancellationToken);
    }

    #endregion

    #region Bulk Operations

    public async Task<int> BulkUpdateStatusAsync(List<int> motelIds, MotelStatus status, CancellationToken cancellationToken = default)
    {
        var motels = await _dbSet
            .Where(m => motelIds.Contains(m.Id))
            .ToListAsync(cancellationToken);

        var now = DateTime.UtcNow;
        foreach (var motel in motels)
        {
            motel.Status = status;
            motel.UpdatedAt = now;
        }

        _dbSet.UpdateRange(motels);
        return motels.Count;
    }

    public async Task<int> BulkUpdateRegionAsync(List<int> motelIds, Region region, CancellationToken cancellationToken = default)
    {
        var motels = await _dbSet
            .Where(m => motelIds.Contains(m.Id))
            .ToListAsync(cancellationToken);

        var now = DateTime.UtcNow;
        foreach (var motel in motels)
        {
            motel.Region = region;
            motel.UpdatedAt = now;
        }

        _dbSet.UpdateRange(motels);
        return motels.Count;
    }

    #endregion
}
