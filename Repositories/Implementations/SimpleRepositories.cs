using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Identity;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;

namespace ams_web_mvc.Repositories.Implementations;

#region Comment Repository

public class CommentRepository : Repository<Comment>, ICommentRepository
{
    public CommentRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<Comment>> GetByStatusAsync(CommentStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.Status == status).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetPendingCommentsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(CommentStatus.Pending, cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetApprovedCommentsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(CommentStatus.Approved, cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetRejectedCommentsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(CommentStatus.Rejected, cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.MotelId == motelId).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetApprovedByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.MotelId == motelId && c.Status == CommentStatus.Approved).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetPendingByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.MotelId == motelId && c.Status == CommentStatus.Pending).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<int> GetPendingCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(c => c.MotelId == motelId && c.Status == CommentStatus.Pending, cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.AuthorId == authorId).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetByModeratorIdAsync(string moderatorId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.ModeratorId == moderatorId).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Comment>> GetRecentCommentsAsync(int days = 30, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        return await _dbSet.Where(c => c.CreatedAt >= cutoffDate).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<Comment> ApproveCommentAsync(int commentId, string moderatorId, string? moderationNotes = null, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(commentId, CommentStatus.Approved, moderatorId, moderationNotes, cancellationToken);
    }

    public async Task<Comment> RejectCommentAsync(int commentId, string moderatorId, string rejectionReason, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(commentId, CommentStatus.Rejected, moderatorId, rejectionReason, cancellationToken);
    }

    public async Task<Comment> UpdateStatusAsync(int commentId, CommentStatus status, string moderatorId, string? notes = null, CancellationToken cancellationToken = default)
    {
        var comment = await GetByIdAsync(commentId, cancellationToken);
        if (comment == null) throw new ArgumentException($"Comment with ID {commentId} not found.");

        comment.Status = status;
        comment.ModeratorId = moderatorId;
        comment.ModerationNotes = notes;
        comment.ModeratedAt = DateTime.UtcNow;
        comment.UpdatedAt = DateTime.UtcNow;

        _dbSet.Update(comment);
        return comment;
    }

    public async Task<(IEnumerable<Comment> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null, CommentStatus? status = null, int? motelId = null, string? authorId = null,
        DateTime? startDate = null, DateTime? endDate = null, int pageNumber = 1, int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
            query = query.Where(c => c.Content.Contains(searchTerm));
        if (status.HasValue)
            query = query.Where(c => c.Status == status.Value);
        if (motelId.HasValue)
            query = query.Where(c => c.MotelId == motelId.Value);
        if (!string.IsNullOrWhiteSpace(authorId))
            query = query.Where(c => c.AuthorId == authorId);
        if (startDate.HasValue)
            query = query.Where(c => c.CreatedAt >= startDate.Value);
        if (endDate.HasValue)
            query = query.Where(c => c.CreatedAt <= endDate.Value);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query.OrderByDescending(c => c.CreatedAt).Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Dictionary<CommentStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.GroupBy(c => c.Status).ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(c => c.Status == CommentStatus.Pending, cancellationToken);
    }

    public async Task<int> GetApprovedCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(c => c.Status == CommentStatus.Approved, cancellationToken);
    }

    public async Task<int> GetRejectedCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(c => c.Status == CommentStatus.Rejected, cancellationToken);
    }

    public async Task<Comment?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Motel).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Comment?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Author).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Comment?> GetWithModeratorAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Moderator).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Comment?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.CommentHistories).ThenInclude(h => h.User).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Comment?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Motel).Include(c => c.Author).Include(c => c.Moderator)
            .Include(c => c.CommentHistories).ThenInclude(h => h.User).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<int> BulkUpdateStatusAsync(List<int> commentIds, CommentStatus status, string moderatorId, CancellationToken cancellationToken = default)
    {
        var comments = await _dbSet.Where(c => commentIds.Contains(c.Id)).ToListAsync(cancellationToken);
        var now = DateTime.UtcNow;
        foreach (var comment in comments)
        {
            comment.Status = status;
            comment.ModeratorId = moderatorId;
            comment.ModeratedAt = now;
            comment.UpdatedAt = now;
        }
        _dbSet.UpdateRange(comments);
        return comments.Count;
    }

    public async Task<int> BulkApproveAsync(List<int> commentIds, string moderatorId, CancellationToken cancellationToken = default)
    {
        return await BulkUpdateStatusAsync(commentIds, CommentStatus.Approved, moderatorId, cancellationToken);
    }

    public async Task<int> BulkRejectAsync(List<int> commentIds, string moderatorId, string rejectionReason, CancellationToken cancellationToken = default)
    {
        return await BulkUpdateStatusAsync(commentIds, CommentStatus.Rejected, moderatorId, cancellationToken);
    }
}

#endregion

#region Caution Repository

public class CautionRepository : Repository<Caution>, ICautionRepository
{
    public CautionRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<Caution>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.MotelId == motelId).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(c => c.MotelId == motelId, cancellationToken);
    }

    public async Task<IEnumerable<Caution>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.AuthorId == authorId).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Caution>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Caution>> GetRecentCautionsAsync(int days = 30, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        return await _dbSet.Where(c => c.CreatedAt >= cutoffDate).OrderByDescending(c => c.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Caution> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null, int? motelId = null, string? authorId = null,
        DateTime? startDate = null, DateTime? endDate = null, int pageNumber = 1, int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
            query = query.Where(c => c.Title.Contains(searchTerm) || c.Description.Contains(searchTerm));
        if (motelId.HasValue)
            query = query.Where(c => c.MotelId == motelId.Value);
        if (!string.IsNullOrWhiteSpace(authorId))
            query = query.Where(c => c.AuthorId == authorId);
        if (startDate.HasValue)
            query = query.Where(c => c.CreatedAt >= startDate.Value);
        if (endDate.HasValue)
            query = query.Where(c => c.CreatedAt <= endDate.Value);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query.OrderByDescending(c => c.CreatedAt).Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Caution?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Motel).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Caution?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Author).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Caution?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.CautionHistories).ThenInclude(h => h.User).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Caution?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(c => c.Motel).Include(c => c.Author)
            .Include(c => c.CautionHistories).ThenInclude(h => h.User).FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }
}

#endregion

#region Suggestion Repository

public class SuggestionRepository : Repository<Suggestion>, ISuggestionRepository
{
    public SuggestionRepository(ApplicationDbContext context) : base(context) { }

    public async Task<IEnumerable<Suggestion>> GetByStatusAsync(SuggestionStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.Status == status).OrderByDescending(s => s.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetPendingSuggestionsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(SuggestionStatus.Pending, cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetApprovedSuggestionsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(SuggestionStatus.Approved, cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetRejectedSuggestionsAsync(CancellationToken cancellationToken = default)
    {
        return await GetByStatusAsync(SuggestionStatus.Rejected, cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.MotelId == motelId).OrderByDescending(s => s.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(s => s.MotelId == motelId, cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.AuthorId == authorId).OrderByDescending(s => s.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetByApproverIdAsync(string approverId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.ApproverId == approverId).OrderByDescending(s => s.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(s => s.CreatedAt >= startDate && s.CreatedAt <= endDate).OrderByDescending(s => s.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Suggestion>> GetRecentSuggestionsAsync(int days = 30, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        return await _dbSet.Where(s => s.CreatedAt >= cutoffDate).OrderByDescending(s => s.CreatedAt).ToListAsync(cancellationToken);
    }

    public async Task<Suggestion> ApproveSuggestionAsync(int suggestionId, string approverId, string? approvalNotes = null, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(suggestionId, SuggestionStatus.Approved, approverId, approvalNotes, cancellationToken);
    }

    public async Task<Suggestion> RejectSuggestionAsync(int suggestionId, string approverId, string rejectionReason, CancellationToken cancellationToken = default)
    {
        return await UpdateStatusAsync(suggestionId, SuggestionStatus.Rejected, approverId, rejectionReason, cancellationToken);
    }

    public async Task<Suggestion> UpdateStatusAsync(int suggestionId, SuggestionStatus status, string userId, string? notes = null, CancellationToken cancellationToken = default)
    {
        var suggestion = await GetByIdAsync(suggestionId, cancellationToken);
        if (suggestion == null) throw new ArgumentException($"Suggestion with ID {suggestionId} not found.");

        suggestion.Status = status;
        suggestion.UpdatedAt = DateTime.UtcNow;

        if (status == SuggestionStatus.Approved || status == SuggestionStatus.Rejected)
        {
            suggestion.ApproverId = userId;
            suggestion.ApprovedAt = DateTime.UtcNow;
            suggestion.ApprovalNotes = notes;
        }

        _dbSet.Update(suggestion);
        return suggestion;
    }

    public async Task<(IEnumerable<Suggestion> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null, SuggestionStatus? status = null, int? motelId = null, string? authorId = null,
        DateTime? startDate = null, DateTime? endDate = null, int pageNumber = 1, int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
            query = query.Where(s => s.Title.Contains(searchTerm) || s.Description.Contains(searchTerm));
        if (status.HasValue)
            query = query.Where(s => s.Status == status.Value);
        if (motelId.HasValue)
            query = query.Where(s => s.MotelId == motelId.Value);
        if (!string.IsNullOrWhiteSpace(authorId))
            query = query.Where(s => s.AuthorId == authorId);
        if (startDate.HasValue)
            query = query.Where(s => s.CreatedAt >= startDate.Value);
        if (endDate.HasValue)
            query = query.Where(s => s.CreatedAt <= endDate.Value);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query.OrderByDescending(s => s.CreatedAt).Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Dictionary<SuggestionStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.GroupBy(s => s.Status).ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(s => s.Status == SuggestionStatus.Pending, cancellationToken);
    }

    public async Task<Suggestion?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(s => s.Motel).FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<Suggestion?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(s => s.Author).FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<Suggestion?> GetWithApproverAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(s => s.Approver).FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<Suggestion?> GetWithHistoryAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(s => s.SuggestionHistories).ThenInclude(h => h.User).FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<Suggestion?> GetWithAllRelatedDataAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Include(s => s.Motel).Include(s => s.Author).Include(s => s.Approver)
            .Include(s => s.SuggestionHistories).ThenInclude(h => h.User).FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<int> BulkUpdateStatusAsync(List<int> suggestionIds, SuggestionStatus status, string userId, CancellationToken cancellationToken = default)
    {
        var suggestions = await _dbSet.Where(s => suggestionIds.Contains(s.Id)).ToListAsync(cancellationToken);
        var now = DateTime.UtcNow;
        foreach (var suggestion in suggestions)
        {
            suggestion.Status = status;
            suggestion.UpdatedAt = now;
            if (status == SuggestionStatus.Approved || status == SuggestionStatus.Rejected)
            {
                suggestion.ApproverId = userId;
                suggestion.ApprovedAt = now;
            }
        }
        _dbSet.UpdateRange(suggestions);
        return suggestions.Count;
    }
}

#endregion
