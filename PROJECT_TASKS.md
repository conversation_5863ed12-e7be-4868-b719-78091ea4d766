# AMS Web MVC - Project Task Breakdown

## Project Overview

Rebuilding the Motel Management System (AMS) from Rails/Vue to .NET MVC with MSSQL database.

---

## 1. Project Setup and Core Infrastructure

### 1.1 Database Setup and Entity Framework Configuration ✅

- [x] Configure Entity Framework Core with MSSQL
- [x] Set up connection strings and configuration
- [x] Add required NuGet packages (EF Core, Identity, JWT, Hangfire, etc.)
- [x] Create ApplicationDbContext with proper configuration
- [x] Configure Hangfire for background jobs
- [x] Set up AutoMapper and validation

### 1.2 Core Entity Models ✅

- [x] Create enums (UserRole, AlertType, CommentStatus, MotelEnums)
- [x] Build base entity classes (BaseEntity, SoftDeletableEntity)
- [x] Implement ApplicationUser with role-based permissions
- [x] Create ApplicationRole for Identity
- [x] Build Motel entity with business logic
- [x] Implement Alert system with workflow states
- [x] Create Comment moderation system
- [x] Build Caution and Suggestion entities
- [x] Create Amenity and AmenityOption models
- [x] Build Service and OrganizationType models
- [x] Implement many-to-many relationship models (MotelAmenity, MotelService)
- [x] Create history/audit tracking entities
- [x] Build notification system entities

### 1.3 Authentication and Authorization System ✅

- [x] Configure ASP.NET Core Identity with custom user/role
- [x] Implement JWT token generation and validation
- [x] Create cookie-based authentication for MVC
- [x] Build dual authentication (JWT for API, Cookies for MVC)
- [x] Implement role-based authorization policies
- [x] Create invitation system for user registration
- [x] Build password expiry and lockout features
- [x] Implement permission matrix based on user roles

### 1.4 Database Migrations and Seed Data ✅

- [x] Create initial database migration
- [x] Build seed data for roles and permissions
- [x] Create seed data for amenities and organization types
- [x] Implement sample data for development
- [x] Create database initialization service

---

## 2. Business Logic and Services Layer

### 2.1 Repository Pattern and Data Access

- [ ] Create generic repository interface and implementation
- [ ] Build specific repositories (MotelRepository, AlertRepository, etc.)
- [ ] Implement Unit of Work pattern
- [ ] Create query objects for complex filtering
- [ ] Build search and filtering capabilities

### 2.2 Core Business Services

- [ ] Create MotelService for business logic
- [ ] Build AlertService with workflow management
- [ ] Implement CommentService with moderation
- [ ] Create CautionService and SuggestionService
- [ ] Build UserService for user management
- [ ] Implement AuditService for change tracking

### 2.3 Search and Filtering System

- [ ] Implement full-text search using MSSQL
- [ ] Create advanced filtering for motels
- [ ] Build search by name, suburb, postcode
- [ ] Implement multi-select filtering (services, regions, etc.)
- [ ] Create exclusion filters
- [ ] Build amenity/option combination filtering
- [ ] Implement tab views (All, Suggestions, Alerts, etc.)

---

## 3. Notification System

### 3.1 Notification Infrastructure

- [ ] Create notification creation service
- [ ] Build notification approval workflow
- [ ] Implement notification preferences
- [ ] Create email notification templates
- [ ] Build notification delivery service

### 3.2 Background Jobs and Processing

- [ ] Implement Hangfire job for notification processing
- [ ] Create email delivery background jobs
- [ ] Build batch notification operations
- [ ] Implement notification cleanup jobs
- [ ] Create alert auto-archive job

---

## 4. Controllers and MVC Actions

### 4.1 Authentication Controllers

- [ ] Create AccountController for login/logout
- [ ] Build UserController for user management
- [ ] Implement InvitationController
- [ ] Create API authentication endpoints

### 4.2 Core Entity Controllers

- [ ] Build HomeController with dashboard
- [ ] Create MotelController with CRUD operations
- [ ] Implement AlertController with workflow
- [ ] Build CommentController with moderation
- [ ] Create CautionController and SuggestionController
- [ ] Implement AdminController for system management

### 4.3 API Controllers (Future-ready)

- [ ] Create API base controller with JWT auth
- [ ] Build API versions of all MVC controllers
- [ ] Implement consistent API response format
- [ ] Create API documentation endpoints

---

## 5. Razor Views and UI Components

### 5.1 Layout and Navigation

- [ ] Create main layout with navigation
- [ ] Build responsive navigation menu
- [ ] Implement user authentication UI
- [ ] Create breadcrumb navigation
- [ ] Build notification indicators

### 5.2 Core Entity Views

- [ ] Create dashboard/home page
- [ ] Build motel listing and detail views
- [ ] Implement alert management views
- [ ] Create comment moderation interface
- [ ] Build caution and suggestion views
- [ ] Implement user management interface

### 5.3 Forms and Validation

- [ ] Create motel create/edit forms
- [ ] Build alert reporting forms
- [ ] Implement comment submission forms
- [ ] Create user invitation forms
- [ ] Build search and filter forms
- [ ] Implement client-side validation

### 5.4 Advanced UI Features

- [ ] Create modal dialogs for quick actions
- [ ] Build data tables with sorting/pagination
- [ ] Implement real-time notifications
- [ ] Create export functionality
- [ ] Build print-friendly views

---

## 6. Testing and Quality Assurance

### 6.1 Unit Testing

- [ ] Set up testing framework (xUnit)
- [ ] Create unit tests for services
- [ ] Build unit tests for repositories
- [ ] Test business logic and validation
- [ ] Create mock data helpers

### 6.2 Integration Testing

- [ ] Set up integration test environment
- [ ] Create database integration tests
- [ ] Build controller integration tests
- [ ] Test authentication and authorization
- [ ] Create end-to-end workflow tests

### 6.3 Performance and Security Testing

- [ ] Implement performance benchmarks
- [ ] Create security vulnerability tests
- [ ] Build load testing scenarios
- [ ] Test data access performance
- [ ] Validate input sanitization

---

## 7. Deployment and Configuration

### 7.1 Environment Configuration

- [ ] Set up development environment
- [ ] Create staging environment configuration
- [ ] Build production environment setup
- [ ] Implement environment-specific settings
- [ ] Create deployment scripts

### 7.2 Security and Monitoring

- [ ] Implement logging with Serilog
- [ ] Set up error tracking and monitoring
- [ ] Create security headers and policies
- [ ] Build audit logging
- [ ] Implement health checks

### 7.3 Documentation and Maintenance

- [ ] Create user documentation
- [ ] Build developer documentation
- [ ] Create deployment guides
- [ ] Implement backup and recovery procedures
- [ ] Create maintenance procedures

---

## Current Status

- **Completed**: 2/9 major sections
- **In Progress**: Authentication and Authorization System
- **Next Priority**: Database Migrations and Seed Data

## Notes

- Project uses .NET 8 with Entity Framework Core
- Database: Microsoft SQL Server
- Authentication: Dual approach (JWT for API, Cookies for MVC)
- Background Jobs: Hangfire
- UI Framework: Bootstrap with Razor Views
