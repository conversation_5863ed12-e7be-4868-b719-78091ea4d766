using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ams_web_mvc.Services.Interfaces;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.ViewModels;

namespace ams_web_mvc.Controllers;

/// <summary>
/// Controller for motel management operations
/// </summary>
[Authorize]
public class MotelController : Controller
{
    private readonly IMotelService _motelService;
    private readonly ISearchService _searchService;
    private readonly IAlertService _alertService;
    private readonly ICommentService _commentService;
    private readonly ILogger<MotelController> _logger;

    public MotelController(
        IMotelService motelService,
        ISearchService searchService,
        IAlertService alertService,
        ICommentService commentService,
        ILogger<MotelController> logger)
    {
        _motelService = motelService ?? throw new ArgumentNullException(nameof(motelService));
        _searchService = searchService ?? throw new ArgumentNullException(nameof(searchService));
        _alertService = alertService ?? throw new ArgumentNullException(nameof(alertService));
        _commentService = commentService ?? throw new ArgumentNullException(nameof(commentService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// List all motels with search and filtering
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Index(string? search, Region? region, int page = 1, int size = 20)
    {
        try
        {
            var viewModel = new MotelListViewModel
            {
                SearchTerm = search,
                SelectedRegion = region,
                PageNumber = page,
                PageSize = size
            };

            // Perform search/filtering
            var searchResults = await _motelService.SearchAsync(
                searchTerm: search,
                region: region,
                pageNumber: page,
                pageSize: size);

            viewModel.Motels = searchResults.Items;
            viewModel.TotalCount = searchResults.TotalCount;
            viewModel.TotalPages = (int)Math.Ceiling((double)searchResults.TotalCount / size);

            // Get filter options
            viewModel.AvailableRegions = Enum.GetValues<Region>().ToList();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading motel list");
            return View(new MotelListViewModel());
        }
    }

    /// <summary>
    /// Show motel details
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var motel = await _motelService.GetWithAllDataAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            var viewModel = new MotelDetailsViewModel
            {
                Motel = motel,
                ActiveAlertsCount = await _alertService.GetCountByMotelAsync(id),
                PendingCommentsCount = await _commentService.GetPendingCountByMotelAsync(id),
                RecentAlerts = (await _alertService.GetByMotelIdAsync(id)).Take(5),
                ApprovedComments = (await _commentService.GetApprovedByMotelIdAsync(id)).Take(10)
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading motel details for ID: {MotelId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Show create motel form
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Moderator")]
    public IActionResult Create()
    {
        var viewModel = new MotelCreateViewModel
        {
            AvailableRegions = Enum.GetValues<Region>().ToList()
        };

        return View(viewModel);
    }

    /// <summary>
    /// Handle motel creation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator,Moderator")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(MotelCreateViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                model.AvailableRegions = Enum.GetValues<Region>().ToList();
                return View(model);
            }

            var motel = new Motel
            {
                Name = model.Name,
                Address = model.Address,
                Suburb = model.Suburb,
                Postcode = model.Postcode,
                Region = model.Region,
                Phone = model.Phone,
                Email = model.Email,
                Website = model.Website,
                Description = model.Description
            };

            var createdMotel = await _motelService.CreateAsync(motel);

            TempData["SuccessMessage"] = $"Motel '{createdMotel.Name}' has been created successfully.";
            return RedirectToAction(nameof(Details), new { id = createdMotel.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating motel");
            ModelState.AddModelError("", "An error occurred while creating the motel. Please try again.");
            model.AvailableRegions = Enum.GetValues<Region>().ToList();
            return View(model);
        }
    }

    /// <summary>
    /// Show edit motel form
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Moderator")]
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var motel = await _motelService.GetByIdAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            var viewModel = new MotelEditViewModel
            {
                Id = motel.Id,
                Name = motel.Name,
                Address = motel.Address,
                Suburb = motel.Suburb,
                Postcode = motel.Postcode,
                Region = motel.Region,
                Phone = motel.Phone,
                Email = motel.Email,
                Website = motel.Website,
                Description = motel.Description,
                AvailableRegions = Enum.GetValues<Region>().ToList()
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading motel for edit: {MotelId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Handle motel update
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator,Moderator")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, MotelEditViewModel model)
    {
        try
        {
            if (id != model.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                model.AvailableRegions = Enum.GetValues<Region>().ToList();
                return View(model);
            }

            var motel = await _motelService.GetByIdAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            // Update motel properties
            motel.Name = model.Name;
            motel.Address = model.Address;
            motel.Suburb = model.Suburb;
            motel.Postcode = model.Postcode;
            motel.Region = model.Region;
            motel.Phone = model.Phone;
            motel.Email = model.Email;
            motel.Website = model.Website;
            motel.Description = model.Description;

            await _motelService.UpdateAsync(motel);

            TempData["SuccessMessage"] = $"Motel '{motel.Name}' has been updated successfully.";
            return RedirectToAction(nameof(Details), new { id = motel.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating motel: {MotelId}", id);
            ModelState.AddModelError("", "An error occurred while updating the motel. Please try again.");
            model.AvailableRegions = Enum.GetValues<Region>().ToList();
            return View(model);
        }
    }

    /// <summary>
    /// Show delete confirmation
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var motel = await _motelService.GetByIdAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            var viewModel = new MotelDeleteViewModel
            {
                Motel = motel,
                HasActiveAlerts = await _motelService.HasActiveAlertsAsync(id),
                ActiveAlertsCount = await _motelService.GetActiveAlertCountAsync(id),
                PendingCommentsCount = await _motelService.GetPendingCommentCountAsync(id)
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading motel for delete: {MotelId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Handle motel deletion
    /// </summary>
    [HttpPost, ActionName("Delete")]
    [Authorize(Roles = "Administrator")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var motel = await _motelService.GetByIdAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            var success = await _motelService.DeleteAsync(id);
            if (success)
            {
                TempData["SuccessMessage"] = $"Motel '{motel.Name}' has been deleted successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to delete the motel. It may have active alerts or other dependencies.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting motel: {MotelId}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the motel. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Manage motel amenities
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Moderator")]
    public async Task<IActionResult> ManageAmenities(int id)
    {
        try
        {
            var motel = await _motelService.GetWithAmenitiesAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            var viewModel = new MotelAmenitiesViewModel
            {
                Motel = motel,
                CurrentAmenities = motel.MotelAmenities?.ToList() ?? new List<MotelAmenity>()
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading amenities for motel: {MotelId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Manage motel services
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Moderator")]
    public async Task<IActionResult> ManageServices(int id)
    {
        try
        {
            var motel = await _motelService.GetWithServicesAsync(id);
            if (motel == null)
            {
                return NotFound();
            }

            var viewModel = new MotelServicesViewModel
            {
                Motel = motel,
                CurrentServices = motel.MotelServices?.ToList() ?? new List<MotelService>()
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading services for motel: {MotelId}", id);
            return NotFound();
        }
    }
}
