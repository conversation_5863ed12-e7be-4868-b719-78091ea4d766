using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using ams_web_mvc.Services.Interfaces;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.ViewModels;

namespace ams_web_mvc.Controllers;

/// <summary>
/// Controller for comment management and moderation
/// </summary>
[Authorize]
public class CommentController : Controller
{
    private readonly ICommentService _commentService;
    private readonly IMotelService _motelService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<CommentController> _logger;

    public CommentController(
        ICommentService commentService,
        IMotelService motelService,
        UserManager<ApplicationUser> userManager,
        ILogger<CommentController> logger)
    {
        _commentService = commentService ?? throw new ArgumentNullException(nameof(commentService));
        _motelService = motelService ?? throw new ArgumentNullException(nameof(motelService));
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// List all comments with filtering and moderation
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Index(string? search, CommentStatus? status, int page = 1, int size = 20)
    {
        try
        {
            var viewModel = new CommentListViewModel
            {
                SearchTerm = search,
                SelectedStatus = status,
                PageNumber = page,
                PageSize = size
            };

            // Perform search/filtering
            var searchResults = await _commentService.SearchAsync(
                searchTerm: search,
                status: status,
                pageNumber: page,
                pageSize: size);

            viewModel.Comments = searchResults.Items;
            viewModel.TotalCount = searchResults.TotalCount;
            viewModel.TotalPages = (int)Math.Ceiling((double)searchResults.TotalCount / size);

            // Get filter options
            viewModel.AvailableStatuses = Enum.GetValues<CommentStatus>().ToList();

            // Get statistics
            viewModel.PendingCount = await _commentService.GetPendingCountAsync();
            viewModel.ApprovedCount = await _commentService.GetApprovedCountAsync();
            viewModel.RejectedCount = await _commentService.GetRejectedCountAsync();

            // Check if user can moderate
            var currentUser = await _userManager.GetUserAsync(User);
            viewModel.CanModerate = await _commentService.CanUserModerateAsync(currentUser?.Id ?? "");

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading comment list");
            return View(new CommentListViewModel());
        }
    }

    /// <summary>
    /// Show comment details
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var comment = await _commentService.GetWithAllDataAsync(id);
            if (comment == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            var viewModel = new CommentDetailsViewModel
            {
                Comment = comment,
                CanEdit = await _commentService.CanUserEditAsync(id, currentUser?.Id ?? ""),
                CanDelete = await _commentService.CanUserDeleteAsync(id, currentUser?.Id ?? ""),
                CanModerate = await _commentService.CanUserModerateAsync(currentUser?.Id ?? "")
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading comment details for ID: {CommentId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Show create comment form
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Create(int? motelId)
    {
        try
        {
            var viewModel = new CommentCreateViewModel
            {
                MotelId = motelId
            };

            // Get available motels for dropdown
            var motels = await _motelService.GetAllAsync();
            viewModel.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();

            // If motelId is provided, get motel details
            if (motelId.HasValue)
            {
                var motel = await _motelService.GetByIdAsync(motelId.Value);
                if (motel != null)
                {
                    viewModel.MotelName = motel.Name;
                }
            }

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading create comment form");
            return View(new CommentCreateViewModel());
        }
    }

    /// <summary>
    /// Handle comment creation
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CommentCreateViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                // Reload dropdown data
                var motels = await _motelService.GetAllAsync();
                model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
                return View(model);
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            var comment = new Comment
            {
                Content = model.Content,
                MotelId = model.MotelId ?? 0,
                AuthorId = currentUser.Id,
                Status = CommentStatus.Pending // All comments start as pending
            };

            var createdComment = await _commentService.CreateAsync(comment, currentUser.Id);

            TempData["SuccessMessage"] = "Your comment has been submitted and is pending moderation.";
            return RedirectToAction("Details", "Motel", new { id = createdComment.MotelId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating comment");
            ModelState.AddModelError("", "An error occurred while creating the comment. Please try again.");
            
            // Reload dropdown data
            var motels = await _motelService.GetAllAsync();
            model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
            return View(model);
        }
    }

    /// <summary>
    /// Show edit comment form
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var comment = await _commentService.GetByIdAsync(id);
            if (comment == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            var canEdit = await _commentService.CanUserEditAsync(id, currentUser?.Id ?? "");
            
            if (!canEdit)
            {
                return Forbid();
            }

            var viewModel = new CommentEditViewModel
            {
                Id = comment.Id,
                Content = comment.Content,
                MotelId = comment.MotelId
            };

            // Get available motels for dropdown
            var motels = await _motelService.GetAllAsync();
            viewModel.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading comment for edit: {CommentId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Handle comment update
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, CommentEditViewModel model)
    {
        try
        {
            if (id != model.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                // Reload dropdown data
                var motels = await _motelService.GetAllAsync();
                model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
                return View(model);
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            var comment = await _commentService.GetByIdAsync(id);
            if (comment == null)
            {
                return NotFound();
            }

            // Update comment properties
            comment.Content = model.Content;
            comment.Status = CommentStatus.Pending; // Reset to pending after edit

            await _commentService.UpdateAsync(comment, currentUser.Id);

            TempData["SuccessMessage"] = "Your comment has been updated and is pending moderation.";
            return RedirectToAction(nameof(Details), new { id = comment.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating comment: {CommentId}", id);
            ModelState.AddModelError("", "An error occurred while updating the comment. Please try again.");
            
            // Reload dropdown data
            var motels = await _motelService.GetAllAsync();
            model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
            return View(model);
        }
    }

    /// <summary>
    /// Approve a comment (moderators only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator,Moderator")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Approve(int id, string? moderationNotes)
    {
        try
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            await _commentService.ApproveAsync(id, currentUser.Id, moderationNotes);
            TempData["SuccessMessage"] = "Comment has been approved successfully.";

            return RedirectToAction(nameof(Details), new { id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving comment: {CommentId}", id);
            TempData["ErrorMessage"] = "An error occurred while approving the comment.";
            return RedirectToAction(nameof(Details), new { id });
        }
    }

    /// <summary>
    /// Reject a comment (moderators only)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator,Moderator")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Reject(int id, string rejectionReason)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(rejectionReason))
            {
                TempData["ErrorMessage"] = "Rejection reason is required.";
                return RedirectToAction(nameof(Details), new { id });
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            await _commentService.RejectAsync(id, currentUser.Id, rejectionReason);
            TempData["SuccessMessage"] = "Comment has been rejected successfully.";

            return RedirectToAction(nameof(Details), new { id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting comment: {CommentId}", id);
            TempData["ErrorMessage"] = "An error occurred while rejecting the comment.";
            return RedirectToAction(nameof(Details), new { id });
        }
    }

    /// <summary>
    /// Delete a comment
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            var comment = await _commentService.GetByIdAsync(id);
            if (comment == null)
            {
                return NotFound();
            }

            var success = await _commentService.DeleteAsync(id, currentUser.Id);
            if (success)
            {
                TempData["SuccessMessage"] = "Comment has been deleted successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to delete the comment.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting comment: {CommentId}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the comment.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Moderation dashboard for moderators
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Moderator")]
    public async Task<IActionResult> Moderation()
    {
        try
        {
            var viewModel = new CommentModerationViewModel();

            // Get pending comments
            viewModel.PendingComments = (await _commentService.GetPendingAsync()).Take(20);
            
            // Get statistics
            viewModel.PendingCount = await _commentService.GetPendingCountAsync();
            viewModel.ApprovedCount = await _commentService.GetApprovedCountAsync();
            viewModel.RejectedCount = await _commentService.GetRejectedCountAsync();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading moderation dashboard");
            return View(new CommentModerationViewModel());
        }
    }
}
