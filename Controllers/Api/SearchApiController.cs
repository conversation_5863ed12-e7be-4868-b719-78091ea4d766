using Microsoft.AspNetCore.Mvc;
using ams_web_mvc.Services.Interfaces;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Controllers.Api;

/// <summary>
/// API controller for search and filtering operations (AJAX support)
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SearchApiController : ControllerBase
{
    private readonly ISearchService _searchService;
    private readonly IMotelService _motelService;
    private readonly ILogger<SearchApiController> _logger;

    public SearchApiController(
        ISearchService searchService,
        IMotelService motelService,
        ILogger<SearchApiController> logger)
    {
        _searchService = searchService ?? throw new ArgumentNullException(nameof(searchService));
        _motelService = motelService ?? throw new ArgumentNullException(nameof(motelService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get search suggestions for autocomplete
    /// </summary>
    [HttpGet("suggestions")]
    public async Task<IActionResult> GetSuggestions([FromQuery] string term, [FromQuery] int maxResults = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(term) || term.Length < 2)
            {
                return Ok(new string[0]);
            }

            var suggestions = await _searchService.GetSearchSuggestionsAsync(term, maxResults);
            return Ok(suggestions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search suggestions for term: {Term}", term);
            return Ok(new string[0]);
        }
    }

    /// <summary>
    /// Perform quick search
    /// </summary>
    [HttpGet("quick")]
    public async Task<IActionResult> QuickSearch([FromQuery] string q, [FromQuery] int page = 1, [FromQuery] int size = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(q))
            {
                return BadRequest("Search term is required");
            }

            var results = await _searchService.FullTextSearchAsync(q, page, size);
            
            var response = new
            {
                items = results.Items.Select(m => new
                {
                    id = m.Id,
                    name = m.Name,
                    suburb = m.Suburb,
                    postcode = m.Postcode,
                    region = m.Region.ToString()
                }),
                totalCount = results.TotalCount,
                pageNumber = page,
                pageSize = size,
                totalPages = (int)Math.Ceiling((double)results.TotalCount / size)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing quick search for: {SearchTerm}", q);
            return StatusCode(500, "An error occurred while searching");
        }
    }

    /// <summary>
    /// Get search statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetStatistics()
    {
        try
        {
            var stats = await _searchService.GetSearchStatisticsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search statistics");
            return StatusCode(500, "An error occurred while getting statistics");
        }
    }

    /// <summary>
    /// Get motels by region
    /// </summary>
    [HttpGet("region/{region}")]
    public async Task<IActionResult> GetByRegion(Region region, [FromQuery] int page = 1, [FromQuery] int size = 20)
    {
        try
        {
            var results = await _searchService.SearchByLocationAsync(region: region, pageNumber: page, pageSize: size);
            
            var response = new
            {
                items = results.Items.Select(m => new
                {
                    id = m.Id,
                    name = m.Name,
                    suburb = m.Suburb,
                    postcode = m.Postcode,
                    region = m.Region.ToString()
                }),
                totalCount = results.TotalCount,
                pageNumber = page,
                pageSize = size,
                totalPages = (int)Math.Ceiling((double)results.TotalCount / size)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels by region: {Region}", region);
            return StatusCode(500, "An error occurred while getting motels by region");
        }
    }

    /// <summary>
    /// Get motels with alerts
    /// </summary>
    [HttpGet("with-alerts")]
    public async Task<IActionResult> GetWithAlerts([FromQuery] int page = 1, [FromQuery] int size = 20)
    {
        try
        {
            var results = await _searchService.GetMotelsWithAlertsAsync(page, size);
            
            var response = new
            {
                items = results.Items.Select(m => new
                {
                    id = m.Id,
                    name = m.Name,
                    suburb = m.Suburb,
                    postcode = m.Postcode,
                    region = m.Region.ToString(),
                    alertCount = m.Alerts?.Count(a => !a.IsClosed) ?? 0
                }),
                totalCount = results.TotalCount,
                pageNumber = page,
                pageSize = size,
                totalPages = (int)Math.Ceiling((double)results.TotalCount / size)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels with alerts");
            return StatusCode(500, "An error occurred while getting motels with alerts");
        }
    }

    /// <summary>
    /// Get motels with comments
    /// </summary>
    [HttpGet("with-comments")]
    public async Task<IActionResult> GetWithComments([FromQuery] int page = 1, [FromQuery] int size = 20)
    {
        try
        {
            var results = await _searchService.GetMotelsWithCommentsAsync(page, size);
            
            var response = new
            {
                items = results.Items.Select(m => new
                {
                    id = m.Id,
                    name = m.Name,
                    suburb = m.Suburb,
                    postcode = m.Postcode,
                    region = m.Region.ToString(),
                    commentCount = m.Comments?.Count(c => c.Status == CommentStatus.Approved) ?? 0
                }),
                totalCount = results.TotalCount,
                pageNumber = page,
                pageSize = size,
                totalPages = (int)Math.Ceiling((double)results.TotalCount / size)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels with comments");
            return StatusCode(500, "An error occurred while getting motels with comments");
        }
    }

    /// <summary>
    /// Get motels with suggestions
    /// </summary>
    [HttpGet("with-suggestions")]
    public async Task<IActionResult> GetWithSuggestions([FromQuery] int page = 1, [FromQuery] int size = 20)
    {
        try
        {
            var results = await _searchService.GetMotelsWithSuggestionsAsync(page, size);
            
            var response = new
            {
                items = results.Items.Select(m => new
                {
                    id = m.Id,
                    name = m.Name,
                    suburb = m.Suburb,
                    postcode = m.Postcode,
                    region = m.Region.ToString(),
                    suggestionCount = m.Suggestions?.Count() ?? 0
                }),
                totalCount = results.TotalCount,
                pageNumber = page,
                pageSize = size,
                totalPages = (int)Math.Ceiling((double)results.TotalCount / size)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels with suggestions");
            return StatusCode(500, "An error occurred while getting motels with suggestions");
        }
    }

    /// <summary>
    /// Get popular search terms
    /// </summary>
    [HttpGet("popular-terms")]
    public async Task<IActionResult> GetPopularTerms([FromQuery] int maxResults = 10)
    {
        try
        {
            var terms = await _searchService.GetPopularSearchTermsAsync(maxResults);
            return Ok(terms);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular search terms");
            return StatusCode(500, "An error occurred while getting popular search terms");
        }
    }
}
