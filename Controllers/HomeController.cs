using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ams_web_mvc.Services.Interfaces;
using ams_web_mvc.Models;
using ams_web_mvc.ViewModels;

namespace ams_web_mvc.Controllers;

/// <summary>
/// Home controller with dashboard and main navigation
/// </summary>
public class HomeController : Controller
{
    private readonly IMotelService _motelService;
    private readonly IAlertService _alertService;
    private readonly ICommentService _commentService;
    private readonly ISearchService _searchService;
    private readonly ILogger<HomeController> _logger;

    public HomeController(
        IMotelService motelService,
        IAlertService alertService,
        ICommentService commentService,
        ISearchService searchService,
        ILogger<HomeController> logger)
    {
        _motelService = motelService ?? throw new ArgumentNullException(nameof(motelService));
        _alertService = alertService ?? throw new ArgumentNullException(nameof(alertService));
        _commentService = commentService ?? throw new ArgumentNullException(nameof(commentService));
        _searchService = searchService ?? throw new ArgumentNullException(nameof(searchService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Dashboard home page
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Index()
    {
        try
        {
            var viewModel = new DashboardViewModel();

            // Get dashboard statistics
            var searchStats = await _searchService.GetSearchStatisticsAsync();
            viewModel.TotalMotels = searchStats.TotalMotels;
            viewModel.MotelsWithAlerts = searchStats.MotelsWithAlerts;
            viewModel.MotelsWithComments = searchStats.MotelsWithComments;
            viewModel.MotelsWithSuggestions = searchStats.MotelsWithSuggestions;
            viewModel.MotelsByRegion = searchStats.MotelsByRegion;

            // Get recent activity
            viewModel.RecentAlerts = (await _alertService.GetRecentAlertsAsync(7)).Take(5);
            viewModel.PendingComments = (await _commentService.GetPendingAsync()).Take(5);
            viewModel.PopularSearchTerms = await _searchService.GetPopularSearchTermsAsync(10);

            // Get quick stats
            viewModel.ActiveAlertsCount = await _alertService.GetActiveCountAsync();
            viewModel.PendingCommentsCount = await _commentService.GetPendingCountAsync();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard");
            return View(new DashboardViewModel());
        }
    }

    /// <summary>
    /// Search page with advanced filtering
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Search(string? q, int page = 1, int size = 20)
    {
        try
        {
            var viewModel = new SearchViewModel
            {
                SearchTerm = q,
                PageNumber = page,
                PageSize = size
            };

            // Perform search
            if (!string.IsNullOrWhiteSpace(q))
            {
                var searchResults = await _searchService.FullTextSearchAsync(q, page, size);
                viewModel.Results = searchResults.Items;
                viewModel.TotalCount = searchResults.TotalCount;
                viewModel.TotalPages = (int)Math.Ceiling((double)searchResults.TotalCount / size);
            }
            else
            {
                // Show all motels if no search term
                var allMotels = await _motelService.SearchAsync(pageNumber: page, pageSize: size);
                viewModel.Results = allMotels.Items;
                viewModel.TotalCount = allMotels.TotalCount;
                viewModel.TotalPages = (int)Math.Ceiling((double)allMotels.TotalCount / size);
            }

            // Get search suggestions for autocomplete
            if (!string.IsNullOrWhiteSpace(q) && q.Length >= 2)
            {
                viewModel.Suggestions = await _searchService.GetSearchSuggestionsAsync(q);
            }

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing search for term: {SearchTerm}", q);
            return View(new SearchViewModel { SearchTerm = q });
        }
    }

    /// <summary>
    /// Privacy policy page
    /// </summary>
    [HttpGet]
    public IActionResult Privacy()
    {
        return View();
    }

    /// <summary>
    /// Error page
    /// </summary>
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel
        {
            RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier
        });
    }
}
