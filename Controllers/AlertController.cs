using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using ams_web_mvc.Services.Interfaces;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.ViewModels;

namespace ams_web_mvc.Controllers;

/// <summary>
/// Controller for alert management operations
/// </summary>
[Authorize]
public class AlertController : Controller
{
    private readonly IAlertService _alertService;
    private readonly IMotelService _motelService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<AlertController> _logger;

    public AlertController(
        IAlertService alertService,
        IMotelService motelService,
        UserManager<ApplicationUser> userManager,
        ILogger<AlertController> logger)
    {
        _alertService = alertService ?? throw new ArgumentNullException(nameof(alertService));
        _motelService = motelService ?? throw new ArgumentNullException(nameof(motelService));
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// List all alerts with filtering
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Index(string? search, AlertType? type, bool? isActive, int page = 1, int size = 20)
    {
        try
        {
            var viewModel = new AlertListViewModel
            {
                SearchTerm = search,
                SelectedType = type,
                IsActiveFilter = isActive,
                PageNumber = page,
                PageSize = size
            };

            // Perform search/filtering
            var searchResults = await _alertService.SearchAsync(
                searchTerm: search,
                alertType: type,
                isActive: isActive,
                pageNumber: page,
                pageSize: size);

            viewModel.Alerts = searchResults.Items;
            viewModel.TotalCount = searchResults.TotalCount;
            viewModel.TotalPages = (int)Math.Ceiling((double)searchResults.TotalCount / size);

            // Get filter options
            viewModel.AvailableTypes = Enum.GetValues<AlertType>().ToList();

            // Get statistics
            viewModel.ActiveCount = await _alertService.GetActiveCountAsync();
            viewModel.ClosedCount = await _alertService.GetClosedCountAsync();
            viewModel.ArchivedCount = await _alertService.GetArchivedCountAsync();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading alert list");
            return View(new AlertListViewModel());
        }
    }

    /// <summary>
    /// Show alert details
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var alert = await _alertService.GetWithAllDataAsync(id);
            if (alert == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            var viewModel = new AlertDetailsViewModel
            {
                Alert = alert,
                CanEdit = await _alertService.CanUserEditAsync(id, currentUser?.Id ?? ""),
                CanDelete = await _alertService.CanUserDeleteAsync(id, currentUser?.Id ?? ""),
                IsExpired = await _alertService.IsExpiredAsync(id)
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading alert details for ID: {AlertId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Show create alert form
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Create(int? motelId)
    {
        try
        {
            var viewModel = new AlertCreateViewModel
            {
                MotelId = motelId,
                AvailableTypes = Enum.GetValues<AlertType>().ToList()
            };

            // Get available motels for dropdown
            var motels = await _motelService.GetAllAsync();
            viewModel.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();

            // If motelId is provided, get motel details
            if (motelId.HasValue)
            {
                var motel = await _motelService.GetByIdAsync(motelId.Value);
                if (motel != null)
                {
                    viewModel.MotelName = motel.Name;
                }
            }

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading create alert form");
            return View(new AlertCreateViewModel());
        }
    }

    /// <summary>
    /// Handle alert creation
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(AlertCreateViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                // Reload dropdown data
                var motels = await _motelService.GetAllAsync();
                model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
                model.AvailableTypes = Enum.GetValues<AlertType>().ToList();
                return View(model);
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            var alert = new Alert
            {
                Title = model.Title,
                Description = model.Description,
                Type = model.Type,
                MotelId = model.MotelId ?? 0,
                ExpiryDate = model.ExpiryDate,
                UserId = currentUser.Id
            };

            var createdAlert = await _alertService.CreateAsync(alert, currentUser.Id);

            TempData["SuccessMessage"] = $"Alert '{createdAlert.Title}' has been created successfully.";
            return RedirectToAction(nameof(Details), new { id = createdAlert.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert");
            ModelState.AddModelError("", "An error occurred while creating the alert. Please try again.");
            
            // Reload dropdown data
            var motels = await _motelService.GetAllAsync();
            model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
            model.AvailableTypes = Enum.GetValues<AlertType>().ToList();
            return View(model);
        }
    }

    /// <summary>
    /// Show edit alert form
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var alert = await _alertService.GetByIdAsync(id);
            if (alert == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            var canEdit = await _alertService.CanUserEditAsync(id, currentUser?.Id ?? "");
            
            if (!canEdit)
            {
                return Forbid();
            }

            var viewModel = new AlertEditViewModel
            {
                Id = alert.Id,
                Title = alert.Title,
                Description = alert.Description,
                Type = alert.Type,
                MotelId = alert.MotelId,
                ExpiryDate = alert.ExpiryDate,
                AvailableTypes = Enum.GetValues<AlertType>().ToList()
            };

            // Get available motels for dropdown
            var motels = await _motelService.GetAllAsync();
            viewModel.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading alert for edit: {AlertId}", id);
            return NotFound();
        }
    }

    /// <summary>
    /// Handle alert update
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, AlertEditViewModel model)
    {
        try
        {
            if (id != model.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                // Reload dropdown data
                var motels = await _motelService.GetAllAsync();
                model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
                model.AvailableTypes = Enum.GetValues<AlertType>().ToList();
                return View(model);
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            var alert = await _alertService.GetByIdAsync(id);
            if (alert == null)
            {
                return NotFound();
            }

            // Update alert properties
            alert.Title = model.Title;
            alert.Description = model.Description;
            alert.Type = model.Type;
            alert.MotelId = model.MotelId;
            alert.ExpiryDate = model.ExpiryDate;

            await _alertService.UpdateAsync(alert, currentUser.Id);

            TempData["SuccessMessage"] = $"Alert '{alert.Title}' has been updated successfully.";
            return RedirectToAction(nameof(Details), new { id = alert.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating alert: {AlertId}", id);
            ModelState.AddModelError("", "An error occurred while updating the alert. Please try again.");
            
            // Reload dropdown data
            var motels = await _motelService.GetAllAsync();
            model.AvailableMotels = motels.Select(m => new { m.Id, m.Name }).ToList();
            model.AvailableTypes = Enum.GetValues<AlertType>().ToList();
            return View(model);
        }
    }

    /// <summary>
    /// Close an alert
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Close(int id)
    {
        try
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            await _alertService.CloseAlertAsync(id, currentUser.Id);
            TempData["SuccessMessage"] = "Alert has been closed successfully.";

            return RedirectToAction(nameof(Details), new { id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing alert: {AlertId}", id);
            TempData["ErrorMessage"] = "An error occurred while closing the alert.";
            return RedirectToAction(nameof(Details), new { id });
        }
    }

    /// <summary>
    /// Reopen an alert
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Reopen(int id)
    {
        try
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            await _alertService.ReopenAlertAsync(id, currentUser.Id);
            TempData["SuccessMessage"] = "Alert has been reopened successfully.";

            return RedirectToAction(nameof(Details), new { id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reopening alert: {AlertId}", id);
            TempData["ErrorMessage"] = "An error occurred while reopening the alert.";
            return RedirectToAction(nameof(Details), new { id });
        }
    }

    /// <summary>
    /// Delete an alert
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            var alert = await _alertService.GetByIdAsync(id);
            if (alert == null)
            {
                return NotFound();
            }

            var success = await _alertService.DeleteAsync(id, currentUser.Id);
            if (success)
            {
                TempData["SuccessMessage"] = $"Alert '{alert.Title}' has been deleted successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to delete the alert.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting alert: {AlertId}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the alert.";
            return RedirectToAction(nameof(Index));
        }
    }
}
