using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Hangfire;
using Hangfire.SqlServer;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Configuration;
using ams_web_mvc.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Configuration
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
builder.Services.Configure<ApplicationSettings>(builder.Configuration.GetSection("ApplicationSettings"));

// Database configuration
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Identity configuration
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 8;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Sign in settings
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Dual Authentication: Cookies for MVC, JWT for API
var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
var key = Encoding.ASCII.GetBytes(jwtSettings!.SecretKey);

builder.Services.AddAuthentication(options =>
{
    // Default to cookies for MVC - use Identity's default scheme
    options.DefaultAuthenticateScheme = IdentityConstants.ApplicationScheme;
    options.DefaultChallengeScheme = IdentityConstants.ApplicationScheme;
    options.DefaultSignInScheme = IdentityConstants.ApplicationScheme;
})
.AddJwtBearer("JWT", options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Hangfire for background jobs
builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseSqlServerStorage(builder.Configuration.GetConnectionString("DefaultConnection"), new SqlServerStorageOptions
    {
        CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
        SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
        QueuePollInterval = TimeSpan.Zero,
        UseRecommendedIsolationLevel = true,
        DisableGlobalLocks = true
    }));

builder.Services.AddHangfireServer();

// Add MVC services
builder.Services.AddControllersWithViews();

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Register custom services
builder.Services.AddScoped<ams_web_mvc.Services.IAuthenticationService, ams_web_mvc.Services.AuthenticationService>();
builder.Services.AddScoped<ams_web_mvc.Services.IJwtService, ams_web_mvc.Services.JwtService>();
builder.Services.AddScoped<ams_web_mvc.Services.IDatabaseService, ams_web_mvc.Services.DatabaseService>();

// Register repositories
builder.Services.AddScoped(typeof(ams_web_mvc.Repositories.Interfaces.IRepository<>), typeof(ams_web_mvc.Repositories.Implementations.Repository<>));
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IUnitOfWork, ams_web_mvc.Repositories.Implementations.UnitOfWork>();

// Register specific repositories
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IMotelRepository, ams_web_mvc.Repositories.Implementations.MotelRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IAlertRepository, ams_web_mvc.Repositories.Implementations.AlertRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.ICommentRepository, ams_web_mvc.Repositories.Implementations.CommentRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.ICautionRepository, ams_web_mvc.Repositories.Implementations.CautionRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.ISuggestionRepository, ams_web_mvc.Repositories.Implementations.SuggestionRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IUserRepository, ams_web_mvc.Repositories.Implementations.UserRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IRoleRepository, ams_web_mvc.Repositories.Implementations.RoleRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IAmenityRepository, ams_web_mvc.Repositories.Implementations.AmenityRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IAmenityOptionRepository, ams_web_mvc.Repositories.Implementations.AmenityOptionRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IServiceRepository, ams_web_mvc.Repositories.Implementations.ServiceRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IOrganizationTypeRepository, ams_web_mvc.Repositories.Implementations.OrganizationTypeRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IMotelAmenityRepository, ams_web_mvc.Repositories.Implementations.MotelAmenityRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IMotelServiceRepository, ams_web_mvc.Repositories.Implementations.MotelServiceRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.INotificationRepository, ams_web_mvc.Repositories.Implementations.NotificationRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.INotificationPreferenceRepository, ams_web_mvc.Repositories.Implementations.NotificationPreferenceRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IAlertHistoryRepository, ams_web_mvc.Repositories.Implementations.AlertHistoryRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.ICommentHistoryRepository, ams_web_mvc.Repositories.Implementations.CommentHistoryRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.ICautionHistoryRepository, ams_web_mvc.Repositories.Implementations.CautionHistoryRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.ISuggestionHistoryRepository, ams_web_mvc.Repositories.Implementations.SuggestionHistoryRepository>();
builder.Services.AddScoped<ams_web_mvc.Repositories.Interfaces.IMotelChangeLogRepository, ams_web_mvc.Repositories.Implementations.MotelChangeLogRepository>();

// Authorization
builder.Services.AddScoped<IAuthorizationHandler, ams_web_mvc.Authorization.Requirements.RoleRequirementHandler>();

builder.Services.AddAuthorization(options =>
{
    // Role-based policies
    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireUser,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.User)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireManager,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.Manager)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireRestrictedAdmin,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.RestrictedAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireSuperAdmin,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.SuperAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireLeadAdmin,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.LeadAdmin)));

    // Feature-based policies (same as role-based for now, can be customized later)
    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanManageMotels,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.Manager)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanManageAlerts,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.Manager)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanModerateComments,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.LeadAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanManageUsers,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.RestrictedAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanApproveNotifications,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.LeadAdmin)));
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Hangfire Dashboard
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    Authorization = new[] { new HangfireAuthorizationFilter() }
});

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Initialize database
using (var scope = app.Services.CreateScope())
{
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    try
    {
        logger.LogInformation("Starting database initialization...");
        var databaseService = scope.ServiceProvider.GetRequiredService<ams_web_mvc.Services.IDatabaseService>();

        // Test database connection first
        var canConnect = await databaseService.DatabaseExistsAsync();
        if (!canConnect)
        {
            logger.LogWarning("Cannot connect to database. Please ensure SQL Server is running and connection string is correct.");
            logger.LogInformation("Connection String: {ConnectionString}",
                builder.Configuration.GetConnectionString("DefaultConnection")?.Replace("Password=Masuklah#1", "Password=***"));
        }
        else
        {
            try
            {
                await databaseService.InitializeDatabaseAsync();
                var status = await databaseService.GetDatabaseStatusAsync();
                logger.LogInformation("Database initialization completed successfully.");
                logger.LogInformation("Database Status:\n{Status}", status);
            }
            catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Number == 1785) // Cascade delete conflict
            {
                logger.LogWarning("Database schema conflict detected. Resetting database...");
                await databaseService.ResetDatabaseAsync();
                var status = await databaseService.GetDatabaseStatusAsync();
                logger.LogInformation("Database reset completed successfully.");
                logger.LogInformation("Database Status:\n{Status}", status);
            }
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "An error occurred while initializing the database.");
        logger.LogWarning("Application will continue without database initialization. Some features may not work properly.");

        // In production, you might want to throw here to prevent startup with database issues
        if (!app.Environment.IsDevelopment())
        {
            logger.LogCritical("Database initialization failed in production environment. Application startup aborted.");
            throw;
        }
    }
}

app.Run();
