using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Hangfire;
using Hangfire.SqlServer;
using ams_web_mvc.Data;
using ams_web_mvc.Models;
using ams_web_mvc.Configuration;
using ams_web_mvc.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Configuration
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
builder.Services.Configure<ApplicationSettings>(builder.Configuration.GetSection("ApplicationSettings"));

// Database configuration
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Identity configuration
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 8;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Sign in settings
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Dual Authentication: Cookies for MVC, JWT for API
var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
var key = Encoding.ASCII.GetBytes(jwtSettings!.SecretKey);

builder.Services.AddAuthentication(options =>
{
    // Default to cookies for MVC
    options.DefaultAuthenticateScheme = IdentityConstants.ApplicationScheme;
    options.DefaultChallengeScheme = IdentityConstants.ApplicationScheme;
    options.DefaultSignInScheme = IdentityConstants.ApplicationScheme;
})
.AddCookie(IdentityConstants.ApplicationScheme, options =>
{
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.ExpireTimeSpan = TimeSpan.FromHours(12);
    options.SlidingExpiration = true;
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
})
.AddJwtBearer("JWT", options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Hangfire for background jobs
builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseSqlServerStorage(builder.Configuration.GetConnectionString("DefaultConnection"), new SqlServerStorageOptions
    {
        CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
        SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
        QueuePollInterval = TimeSpan.Zero,
        UseRecommendedIsolationLevel = true,
        DisableGlobalLocks = true
    }));

builder.Services.AddHangfireServer();

// Add MVC services
builder.Services.AddControllersWithViews();

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Register custom services
builder.Services.AddScoped<ams_web_mvc.Services.IAuthenticationService, ams_web_mvc.Services.AuthenticationService>();
builder.Services.AddScoped<ams_web_mvc.Services.IJwtService, ams_web_mvc.Services.JwtService>();

// Authorization
builder.Services.AddScoped<IAuthorizationHandler, ams_web_mvc.Authorization.Requirements.RoleRequirementHandler>();

builder.Services.AddAuthorization(options =>
{
    // Role-based policies
    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireUser,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.User)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireManager,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.Manager)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireRestrictedAdmin,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.RestrictedAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireSuperAdmin,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.SuperAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.RequireLeadAdmin,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.LeadAdmin)));

    // Feature-based policies (same as role-based for now, can be customized later)
    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanManageMotels,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.Manager)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanManageAlerts,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.Manager)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanModerateComments,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.LeadAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanManageUsers,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.RestrictedAdmin)));

    options.AddPolicy(ams_web_mvc.Authorization.PolicyNames.CanApproveNotifications,
        policy => policy.Requirements.Add(new ams_web_mvc.Authorization.Requirements.RoleRequirement(ams_web_mvc.Models.Enums.UserRole.LeadAdmin)));
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Hangfire Dashboard
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    Authorization = new[] { new HangfireAuthorizationFilter() }
});

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
