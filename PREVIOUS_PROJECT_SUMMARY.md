# Motel Management System (AMS) - Architecture Summary

## Overview

This is a Ruby on Rails API-only application for managing accommodation facilities (motels) with comprehensive incident reporting, user management, and notification systems. The system supports multi-role access control and workflow management for government/institutional use.

## Technology Stack

### Core Framework

- **Ruby on Rails**: 7.0.4+ (API-only mode)
- **Ruby Version**: 3.2.1
- **Database**: PostgreSQL with full-text search capabilities
- **Web Server**: Puma

### Key Dependencies

- **Authentication**: Devise + Devise-JWT + Devise-Invitable
- **Authorization**: CanCanCan (role-based permissions)
- **Background Jobs**: Sidekiq 6.5
- **Search**: pg_search 2.3+ (PostgreSQL full-text search)
- **Soft Deletes**: Discard 1.2
- **Pagination**: Kaminari 1.2
- **Markdown**: Redcarpet 3.5
- **CORS**: Rack-CORS
- **Error Tracking**: Rollbar
- **Email**: ActionMailer with <PERSON> Opener (development)

## Database Schema

### Core Entities

#### Users

```sql
-- Primary user management table
users (
  id, email, encrypted_password, role, first_name, last_name,
  jti, invitation_token, invitation_accepted_at, failed_attempts,
  locked_at, discarded_at, password_changed_at, primary_contact
)
```

**User Roles Hierarchy:**

- `user` (0) - Basic read access
- `manager` (1) - Content management
- `restricted_admin` (2) - Limited admin access
- `super_admin` (3) - Full admin access
- `lead_admin` (4) - Highest level access + announcements

#### Motels

```sql
-- Core accommodation properties
motels (
  id, name, street, suburb, postcode, state, region,
  phone, email, website, motel_type, density, duration[],
  inactive, created_at, updated_at
)
```

**Business Rules:**

- Required fields: name, street, postcode, suburb, state
- Predefined enums for: density, region, motel_type, duration
- Array field for multiple duration types
- Inactive flag for deactivated properties

#### Alerts (Incident Management)

```sql
-- Critical incident tracking
alerts (
  id, user_id, motel_id, alert_type, date, time, location,
  description, witnesses[], reported_by, caution_issued,
  closed_at, closure_note, closed_user_id, discarded_at
)
```

**Alert Workflow:**

1. **Open**: `closed_at = NULL, discarded_at = NULL`
2. **Closed**: `closed_at != NULL, closed_at > 2.weeks.ago`
3. **Archived**: `discarded_at != NULL OR closed_at < 2.weeks.ago`

**Alert Types:**

- Category 1 (Critical - #760f0f)
- Category 2 (High - #b62525)
- Category 3 (Medium - #f55c59)

#### Comments (Moderation System)

```sql
-- User-generated content with approval workflow
comments (
  id, user_id, motel_id, body, active_status, pinned, archived,
  user_position_title, user_organization_title,
  approved_at, rejected_at, rejection_reason, rejection_note
)
```

**Comment States:**

- `review_required` (0) - Pending moderation
- `approved` (1) - Published
- `rejected` (2) - Declined with reason

#### Cautions & Suggestions

```sql
-- Both follow similar pattern with soft deletion
cautions/suggestions (
  id, user_id, motel_id, description, authorised_by,
  user_organization, user_position_title, discarded_at
)
```

### Supporting Tables

#### Amenities System

```sql
amenities (id, name, amenity_type, help_text)
amenity_options (id, amenity_id, name, display_text, icon, color)
motel_amenities (id, motel_id, amenity_id, amenity_option_id, note)
```

**Amenity Types:**

- `characteristic` - Property characteristics
- `safety` - Safety requirements
- `amenity` - Main facilities
- `additional` - Additional features

#### Services & Organizations

```sql
organization_types (id, name, background_color, color)
services (id, name, organization_type_id)
motel_services (id, motel_id, service_id)
```

#### Notification System

```sql
notifications (
  id, user_id, notification_event, approval_status, is_read,
  motel_id, alert_id, caution_id, suggestion_id, comment_id,
  announcement_id, event_group_key
)

notification_preferences (
  id, user_id, notification_event, email_notification_allowed
)
```

## Authentication & Authorization

### Authentication (Devise + JWT)

```ruby
# JWT Configuration
jwt.expiration_time = 12.hours.to_i
jwt.secret = ENV['DEVISE_JWT_SECRET_KEY']

# Features enabled
devise :invitable, :database_authenticatable, :recoverable,
       :validatable, :lockable, :jwt_authenticatable
```

**Security Features:**

- JWT token-based authentication
- Account lockout after failed attempts
- Password expiry (500 days)
- Invitation-only user creation
- Admin cookie sessions for elevated access

### Authorization (CanCanCan)

#### Permission Matrix

| Role       | Motels         | Alerts | Comments          | Users             | Notifications  |
| ---------- | -------------- | ------ | ----------------- | ----------------- | -------------- |
| User       | Read, Update\* | Read   | Read, Create      | -                 | Read           |
| Manager    | Manage         | Manage | Read, Create, Pin | Read, Discard\*\* | Read           |
| Admin      | Manage         | Manage | Manage            | Manage\*\*\*      | Manage         |
| Lead Admin | Manage         | Manage | Manage            | Manage            | Approve/Reject |

\*Temporary permission during initial data setup  
**Can discard managers and users, not self  
\***Cannot discard self, must have other admins present

## Business Logic & Workflows

### Edit Permissions

```ruby
def editable?(user)
  return false unless approved? # for comments

  if self.created_at >= 3.days.ago
    return true if user.admin? || user.manager? || self.user == user
  end
  false
end
```

### Alert Lifecycle

1. **Creation**: User reports incident
2. **Investigation**: Admin reviews and updates
3. **Closure**: Admin closes with notes
4. **Auto-Archive**: System archives after 2 weeks

### Comment Moderation

1. **Submission**: User creates comment (review_required)
2. **Review**: Lead admin approves/rejects
3. **Notification**: User notified of decision
4. **Publication**: Approved comments visible to all

### Notification Workflow

1. **Event Trigger**: User action creates notification
2. **Review Required**: Restricted admins need approval
3. **Approval/Rejection**: Lead admin decides
4. **Distribution**: Notifications sent to relevant users
5. **Email Delivery**: Background job sends emails

## API Structure

### RESTful Routes

```ruby
# Authentication
POST   /login
DELETE /logout
POST   /signup (invitation-based)

# Core Resources (nested under motels)
GET    /motels
POST   /motels
GET    /motels/:id
PATCH  /motels/:id

# Nested Resources
/motels/:motel_id/alerts
/motels/:motel_id/cautions
/motels/:motel_id/suggestions
/motels/:motel_id/comments

# Admin Routes
/admin/tables/comments (moderation)
/admin/tables/service_crossovers
/admin/tables/group_crossovers

# Notification Management
/notifications
/notification_approvals
/notification_rejections
```

### Response Format

- **Content-Type**: `application/json`
- **Authentication**: JWT Bearer token
- **Error Handling**: Consistent JSON error responses
- **Pagination**: Kaminari with page/per parameters

## Search & Filtering

### PostgreSQL Full-Text Search

```ruby
pg_search_scope :search_by_name,
  against: :name,
  using: { tsearch: { prefix: true } }

pg_search_scope :search_by_suburb_or_postcode,
  against: [:suburb, :postcode],
  using: { tsearch: { prefix: true } }
```

### Advanced Filtering (FilterMotelsQuery)

- **Text Search**: Name, suburb, postcode
- **Multi-Select**: Services, organization types, regions
- **Exclusion Filters**: Exclude specific criteria
- **Amenity Filtering**: Complex amenity/option combinations
- **Tab Views**: All, suggestions, alerts, cautions, inactive
- **Pagination**: 40 items per page with counts

## Background Processing

### Sidekiq Jobs

```ruby
# Primary notification job
CreateNotificationJob.perform_later(
  :notification_event,
  current_user_id,
  motel: motel,
  alert: alert,
  # ... other associations
)
```

**Job Types:**

- Notification creation and distribution
- Email delivery processing
- Batch notification operations

## Data Integrity & Auditing

### Change Tracking

```ruby
# Motel change logs track all modifications
def change_logs(current_user)
  # Field changes (name, address, etc.)
  # Amenity modifications
  # Service updates
  # All with user attribution and timestamps
end
```

### History Tables

- `alert_histories` - Alert modification history
- `caution_histories` - Caution edit history
- `suggestion_histories` - Suggestion changes
- `comment_histories` - Comment edit tracking
- `motel_change_logs` - Comprehensive motel changes

### Soft Deletion

```ruby
# Using Discard gem
include Discard::Model

scope :kept, -> { undiscarded }
scope :discarded, -> { discarded }
```

## Configuration & Deployment

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://...
DATABASE_HOST=localhost
DATABASE_PORT=5432

# Authentication
DEVISE_JWT_SECRET_KEY=...

# Email
MAILER_HOST=example.com
WEB_HOST=www.example.com

# Monitoring
ROLLBAR_ACCESS_TOKEN=...
```

### Application Configuration

```ruby
# config/application.rb
config.time_zone = 'Australia/Melbourne'
config.api_only = true
config.autoload_paths += %W(#{config.root}/app/queries)
```

## Key Business Rules

1. **3-Day Edit Window**: Users can edit their content for 3 days after creation
2. **Alert Auto-Archive**: Closed alerts automatically archive after 2 weeks
3. **Comment Moderation**: All comments require admin approval before publication
4. **Invitation Expiry**: User invitations expire after 2 days
5. **Password Expiry**: User passwords expire after 500 days
6. **Red Cohort Flagging**: Special visual indicators for high-risk client suggestions
7. **Admin Safeguards**: Admins cannot delete themselves if they're the last admin
8. **Notification Approval**: Restricted admin actions require lead admin approval

## Security Considerations

- JWT tokens with 12-hour expiration
- Role-based access control with hierarchical permissions
- Account lockout protection against brute force
- Invitation-only user registration
- Soft deletion for data recovery
- Comprehensive audit trails
- CORS configuration for frontend integration
- Encrypted email unsubscription tokens

## Frontend Integration

The API is designed to work with a separate frontend application:

- JWT-based authentication
- RESTful JSON API
- Real-time notification counts
- File upload support (Active Storage ready)
- CORS enabled for cross-origin requests
- Consistent error response format

This architecture provides a robust foundation for a comprehensive accommodation management system with strong security, audit capabilities, and workflow management suitable for institutional use.
