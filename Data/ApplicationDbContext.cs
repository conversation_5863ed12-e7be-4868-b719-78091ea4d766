using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Models;

namespace ams_web_mvc.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    // Core entities
    public DbSet<Motel> Motels { get; set; }
    public DbSet<Alert> Alerts { get; set; }
    public DbSet<Comment> Comments { get; set; }
    public DbSet<Caution> Cautions { get; set; }
    public DbSet<Suggestion> Suggestions { get; set; }

    // Supporting entities
    public DbSet<Amenity> Amenities { get; set; }
    public DbSet<AmenityOption> AmenityOptions { get; set; }
    public DbSet<MotelAmenity> MotelAmenities { get; set; }
    public DbSet<OrganizationType> OrganizationTypes { get; set; }
    public DbSet<Service> Services { get; set; }
    public DbSet<MotelService> MotelServices { get; set; }

    // Notification system
    public DbSet<Notification> Notifications { get; set; }
    public DbSet<NotificationPreference> NotificationPreferences { get; set; }

    // History/audit tables
    public DbSet<AlertHistory> AlertHistories { get; set; }
    public DbSet<CautionHistory> CautionHistories { get; set; }
    public DbSet<SuggestionHistory> SuggestionHistories { get; set; }
    public DbSet<CommentHistory> CommentHistories { get; set; }
    public DbSet<MotelChangeLog> MotelChangeLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure Identity tables with custom names
        builder.Entity<ApplicationUser>(entity =>
        {
            entity.ToTable("Users");
            entity.Property(e => e.FirstName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.LastName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Role).HasConversion<int>();
            entity.Property(e => e.PasswordChangedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.HasIndex(e => e.Email).IsUnique();
            
            // Soft delete configuration
            entity.HasQueryFilter(u => u.DiscardedAt == null);
        });

        builder.Entity<ApplicationRole>(entity =>
        {
            entity.ToTable("Roles");
        });

        // Configure Motel entity
        builder.Entity<Motel>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Street).HasMaxLength(300).IsRequired();
            entity.Property(e => e.Suburb).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Postcode).HasMaxLength(10).IsRequired();
            entity.Property(e => e.State).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Phone).HasMaxLength(20);
            entity.Property(e => e.Email).HasMaxLength(200);
            entity.Property(e => e.Website).HasMaxLength(500);
            
            // Enum conversions
            entity.Property(e => e.MotelType).HasConversion<string>();
            entity.Property(e => e.Density).HasConversion<string>();
            entity.Property(e => e.Region).HasConversion<string>();
            
            // Array field for duration - stored as JSON
            entity.Property(e => e.Duration)
                .HasConversion(
                    v => string.Join(',', v.Select(d => d.ToString())),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                          .Select(s => Enum.Parse<Models.Enums.Duration>(s))
                          .ToList()
                )
                .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<List<Models.Enums.Duration>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

            entity.HasIndex(e => e.Name);
            entity.HasIndex(e => e.Suburb);
            entity.HasIndex(e => e.Postcode);
        });

        // Configure Alert entity
        builder.Entity<Alert>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AlertType).HasConversion<string>();
            entity.Property(e => e.Date).IsRequired();
            entity.Property(e => e.Time).IsRequired();
            entity.Property(e => e.Location).HasMaxLength(500);
            entity.Property(e => e.Description).IsRequired();
            entity.Property(e => e.ReportedBy).HasMaxLength(200);
            entity.Property(e => e.ClosureNote).HasMaxLength(1000);
            
            // Array field for witnesses - stored as JSON
            entity.Property(e => e.Witnesses)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList()
                )
                .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<List<string>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

            // Relationships
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Motel)
                .WithMany(m => m.Alerts)
                .HasForeignKey(e => e.MotelId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.ClosedByUser)
                .WithMany()
                .HasForeignKey(e => e.ClosedUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Soft delete configuration
            entity.HasQueryFilter(a => a.DiscardedAt == null);
        });

        // Configure Comment entity
        builder.Entity<Comment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Body).IsRequired();
            entity.Property(e => e.ActiveStatus).HasConversion<int>();
            entity.Property(e => e.UserPositionTitle).HasMaxLength(200);
            entity.Property(e => e.UserOrganizationTitle).HasMaxLength(200);
            entity.Property(e => e.RejectionReason).HasMaxLength(500);
            entity.Property(e => e.RejectionNote).HasMaxLength(1000);

            // Relationships
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Motel)
                .WithMany(m => m.Comments)
                .HasForeignKey(e => e.MotelId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure many-to-many relationships
        ConfigureManyToManyRelationships(builder);
        
        // Configure audit/history tables
        ConfigureHistoryTables(builder);
        
        // Configure notification system
        ConfigureNotificationSystem(builder);
    }

    private void ConfigureManyToManyRelationships(ModelBuilder builder)
    {
        // Motel-Amenity relationship
        builder.Entity<MotelAmenity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Motel)
                .WithMany(m => m.MotelAmenities)
                .HasForeignKey(e => e.MotelId);
            entity.HasOne(e => e.Amenity)
                .WithMany()
                .HasForeignKey(e => e.AmenityId);
            entity.HasOne(e => e.AmenityOption)
                .WithMany()
                .HasForeignKey(e => e.AmenityOptionId);
        });

        // Motel-Service relationship
        builder.Entity<MotelService>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Motel)
                .WithMany(m => m.MotelServices)
                .HasForeignKey(e => e.MotelId);
            entity.HasOne(e => e.Service)
                .WithMany()
                .HasForeignKey(e => e.ServiceId);
        });
    }

    private void ConfigureHistoryTables(ModelBuilder builder)
    {
        // Configure history tables
        builder.Entity<AlertHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Alert)
                .WithMany(a => a.AlertHistories)
                .HasForeignKey(e => e.AlertId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        builder.Entity<CommentHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Comment)
                .WithMany(c => c.CommentHistories)
                .HasForeignKey(e => e.CommentId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        builder.Entity<CautionHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Caution)
                .WithMany(c => c.CautionHistories)
                .HasForeignKey(e => e.CautionId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        builder.Entity<SuggestionHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Suggestion)
                .WithMany(s => s.SuggestionHistories)
                .HasForeignKey(e => e.SuggestionId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        builder.Entity<MotelChangeLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Motel)
                .WithMany()
                .HasForeignKey(e => e.MotelId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureNotificationSystem(ModelBuilder builder)
    {
        // Configure notification entities
        builder.Entity<Notification>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.Motel)
                .WithMany()
                .HasForeignKey(e => e.MotelId)
                .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.Alert)
                .WithMany()
                .HasForeignKey(e => e.AlertId)
                .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.Comment)
                .WithMany()
                .HasForeignKey(e => e.CommentId)
                .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.Caution)
                .WithMany()
                .HasForeignKey(e => e.CautionId)
                .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.Suggestion)
                .WithMany()
                .HasForeignKey(e => e.SuggestionId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        builder.Entity<NotificationPreference>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }
}
