using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Data;

public static class DbInitializer
{
    public static async Task InitializeAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<ApplicationRole>>();

        // Ensure database is created
        await context.Database.EnsureCreatedAsync();

        // Seed roles
        await SeedRolesAsync(roleManager);

        // Seed users
        await SeedUsersAsync(userManager);

        // Seed amenities and organization types
        await SeedAmenitiesAsync(context);
        await SeedOrganizationTypesAsync(context);
        await SeedServicesAsync(context);

        await context.SaveChangesAsync();
    }

    private static async Task SeedRolesAsync(RoleManager<ApplicationRole> roleManager)
    {
        var roles = new[]
        {
            new ApplicationRole("User") { Description = "Basic user with read access" },
            new ApplicationRole("Manager") { Description = "Content management access" },
            new ApplicationRole("RestrictedAdmin") { Description = "Limited admin access" },
            new ApplicationRole("SuperAdmin") { Description = "Full admin access" },
            new ApplicationRole("LeadAdmin") { Description = "Highest level access with announcement permissions" }
        };

        foreach (var role in roles)
        {
            if (!await roleManager.RoleExistsAsync(role.Name!))
            {
                await roleManager.CreateAsync(role);
            }
        }
    }

    private static async Task SeedUsersAsync(UserManager<ApplicationUser> userManager)
    {
        // Create default admin user
        var adminEmail = "<EMAIL>";
        if (await userManager.FindByEmailAsync(adminEmail) == null)
        {
            var adminUser = new ApplicationUser
            {
                UserName = adminEmail,
                Email = adminEmail,
                FirstName = "System",
                LastName = "Administrator",
                Role = UserRole.LeadAdmin,
                EmailConfirmed = true,
                PasswordChangedAt = DateTime.UtcNow,
                PrimaryContact = true
            };

            await userManager.CreateAsync(adminUser, "Admin123!");
            await userManager.AddToRoleAsync(adminUser, "LeadAdmin");
        }

        // Create default manager user
        var managerEmail = "<EMAIL>";
        if (await userManager.FindByEmailAsync(managerEmail) == null)
        {
            var managerUser = new ApplicationUser
            {
                UserName = managerEmail,
                Email = managerEmail,
                FirstName = "Test",
                LastName = "Manager",
                Role = UserRole.Manager,
                EmailConfirmed = true,
                PasswordChangedAt = DateTime.UtcNow
            };

            await userManager.CreateAsync(managerUser, "Manager123!");
            await userManager.AddToRoleAsync(managerUser, "Manager");
        }
    }

    private static async Task SeedAmenitiesAsync(ApplicationDbContext context)
    {
        if (await context.Amenities.AnyAsync()) return;

        var amenities = new[]
        {
            // Characteristics
            new Amenity { Name = "Property Type", AmenityType = AmenityType.Characteristic, HelpText = "Type of accommodation property" },
            new Amenity { Name = "Capacity", AmenityType = AmenityType.Characteristic, HelpText = "Number of guests the property can accommodate" },
            new Amenity { Name = "Room Types", AmenityType = AmenityType.Characteristic, HelpText = "Types of rooms available" },
            
            // Safety
            new Amenity { Name = "Fire Safety", AmenityType = AmenityType.Safety, HelpText = "Fire safety equipment and procedures" },
            new Amenity { Name = "Security", AmenityType = AmenityType.Safety, HelpText = "Security measures and equipment" },
            new Amenity { Name = "Emergency Procedures", AmenityType = AmenityType.Safety, HelpText = "Emergency response procedures" },
            
            // Main Amenities
            new Amenity { Name = "Internet", AmenityType = AmenityType.Amenity, HelpText = "Internet connectivity options" },
            new Amenity { Name = "Parking", AmenityType = AmenityType.Amenity, HelpText = "Parking facilities" },
            new Amenity { Name = "Kitchen Facilities", AmenityType = AmenityType.Amenity, HelpText = "Kitchen and cooking facilities" },
            new Amenity { Name = "Laundry", AmenityType = AmenityType.Amenity, HelpText = "Laundry facilities" },
            
            // Additional Features
            new Amenity { Name = "Pool", AmenityType = AmenityType.Additional, HelpText = "Swimming pool facilities" },
            new Amenity { Name = "Gym", AmenityType = AmenityType.Additional, HelpText = "Fitness facilities" },
            new Amenity { Name = "Restaurant", AmenityType = AmenityType.Additional, HelpText = "On-site dining options" },
            new Amenity { Name = "Conference Facilities", AmenityType = AmenityType.Additional, HelpText = "Meeting and conference rooms" }
        };

        context.Amenities.AddRange(amenities);
        await context.SaveChangesAsync();

        // Add amenity options
        await SeedAmenityOptionsAsync(context);
    }

    private static async Task SeedAmenityOptionsAsync(ApplicationDbContext context)
    {
        var internetAmenity = await context.Amenities.FirstAsync(a => a.Name == "Internet");
        var parkingAmenity = await context.Amenities.FirstAsync(a => a.Name == "Parking");
        var kitchenAmenity = await context.Amenities.FirstAsync(a => a.Name == "Kitchen Facilities");

        var amenityOptions = new[]
        {
            // Internet options
            new AmenityOption { AmenityId = internetAmenity.Id, Name = "WiFi", DisplayText = "Free WiFi", Icon = "wifi", Color = "#007bff" },
            new AmenityOption { AmenityId = internetAmenity.Id, Name = "Ethernet", DisplayText = "Wired Internet", Icon = "ethernet", Color = "#28a745" },
            new AmenityOption { AmenityId = internetAmenity.Id, Name = "None", DisplayText = "No Internet", Icon = "x-circle", Color = "#dc3545" },
            
            // Parking options
            new AmenityOption { AmenityId = parkingAmenity.Id, Name = "Free", DisplayText = "Free Parking", Icon = "car", Color = "#28a745" },
            new AmenityOption { AmenityId = parkingAmenity.Id, Name = "Paid", DisplayText = "Paid Parking", Icon = "credit-card", Color = "#ffc107" },
            new AmenityOption { AmenityId = parkingAmenity.Id, Name = "Limited", DisplayText = "Limited Parking", Icon = "clock", Color = "#fd7e14" },
            new AmenityOption { AmenityId = parkingAmenity.Id, Name = "None", DisplayText = "No Parking", Icon = "x-circle", Color = "#dc3545" },
            
            // Kitchen options
            new AmenityOption { AmenityId = kitchenAmenity.Id, Name = "Full", DisplayText = "Full Kitchen", Icon = "house", Color = "#28a745" },
            new AmenityOption { AmenityId = kitchenAmenity.Id, Name = "Kitchenette", DisplayText = "Kitchenette", Icon = "cup-hot", Color = "#17a2b8" },
            new AmenityOption { AmenityId = kitchenAmenity.Id, Name = "Microwave", DisplayText = "Microwave Only", Icon = "square", Color = "#ffc107" },
            new AmenityOption { AmenityId = kitchenAmenity.Id, Name = "None", DisplayText = "No Kitchen", Icon = "x-circle", Color = "#dc3545" }
        };

        context.AmenityOptions.AddRange(amenityOptions);
    }

    private static async Task SeedOrganizationTypesAsync(ApplicationDbContext context)
    {
        if (await context.OrganizationTypes.AnyAsync()) return;

        var organizationTypes = new[]
        {
            new OrganizationType { Name = "Government", BackgroundColor = "#e3f2fd", Color = "#1976d2" },
            new OrganizationType { Name = "Health Services", BackgroundColor = "#f3e5f5", Color = "#7b1fa2" },
            new OrganizationType { Name = "Community Services", BackgroundColor = "#e8f5e8", Color = "#388e3c" },
            new OrganizationType { Name = "Emergency Services", BackgroundColor = "#ffebee", Color = "#d32f2f" },
            new OrganizationType { Name = "Education", BackgroundColor = "#fff3e0", Color = "#f57c00" },
            new OrganizationType { Name = "Legal Services", BackgroundColor = "#fafafa", Color = "#424242" },
            new OrganizationType { Name = "Support Services", BackgroundColor = "#e0f2f1", Color = "#00695c" }
        };

        context.OrganizationTypes.AddRange(organizationTypes);
    }

    private static async Task SeedServicesAsync(ApplicationDbContext context)
    {
        if (await context.Services.AnyAsync()) return;

        var govType = await context.OrganizationTypes.FirstAsync(o => o.Name == "Government");
        var healthType = await context.OrganizationTypes.FirstAsync(o => o.Name == "Health Services");
        var communityType = await context.OrganizationTypes.FirstAsync(o => o.Name == "Community Services");
        var emergencyType = await context.OrganizationTypes.FirstAsync(o => o.Name == "Emergency Services");

        var services = new[]
        {
            // Government Services
            new Service { Name = "Department of Housing", OrganizationTypeId = govType.Id },
            new Service { Name = "Centrelink", OrganizationTypeId = govType.Id },
            new Service { Name = "Immigration Services", OrganizationTypeId = govType.Id },
            new Service { Name = "Local Council", OrganizationTypeId = govType.Id },
            
            // Health Services
            new Service { Name = "Mental Health Services", OrganizationTypeId = healthType.Id },
            new Service { Name = "Drug and Alcohol Services", OrganizationTypeId = healthType.Id },
            new Service { Name = "Community Health Center", OrganizationTypeId = healthType.Id },
            new Service { Name = "Hospital", OrganizationTypeId = healthType.Id },
            
            // Community Services
            new Service { Name = "Salvation Army", OrganizationTypeId = communityType.Id },
            new Service { Name = "Red Cross", OrganizationTypeId = communityType.Id },
            new Service { Name = "Local Community Center", OrganizationTypeId = communityType.Id },
            new Service { Name = "Food Bank", OrganizationTypeId = communityType.Id },
            
            // Emergency Services
            new Service { Name = "Police", OrganizationTypeId = emergencyType.Id },
            new Service { Name = "Fire Department", OrganizationTypeId = emergencyType.Id },
            new Service { Name = "Ambulance", OrganizationTypeId = emergencyType.Id },
            new Service { Name = "Crisis Support", OrganizationTypeId = emergencyType.Id }
        };

        context.Services.AddRange(services);
    }
}
