using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Data;

public static class SampleDataSeeder
{
    public static async Task SeedSampleDataAsync(ApplicationDbContext context)
    {
        if (await context.Motels.AnyAsync()) return; // Don't seed if data already exists

        // Get users for sample data
        var adminUser = await context.Users.FirstAsync(u => u.Email == "<EMAIL>");
        var managerUser = await context.Users.FirstAsync(u => u.Email == "<EMAIL>");

        // Create sample motels
        var motels = new[]
        {
            new Motel
            {
                Name = "City Central Motel",
                Street = "123 Main Street",
                Suburb = "Melbourne",
                Postcode = "3000",
                State = "VIC",
                Region = Region.Metropolitan,
                Phone = "(03) 9123 4567",
                Email = "<EMAIL>",
                Website = "https://www.citycentral.com.au",
                MotelType = MotelType.Motel,
                Density = Density.High,
                Duration = new List<Duration> { Duration.ShortTerm, Duration.MediumTerm },
                Inactive = false
            },
            new Motel
            {
                Name = "Suburban Inn",
                Street = "456 Suburban Road",
                Suburb = "Richmond",
                Postcode = "3121",
                State = "VIC",
                Region = Region.Metropolitan,
                Phone = "(03) 9234 5678",
                Email = "<EMAIL>",
                MotelType = MotelType.Hotel,
                Density = Density.Medium,
                Duration = new List<Duration> { Duration.MediumTerm, Duration.LongTerm },
                Inactive = false
            },
            new Motel
            {
                Name = "Backpacker's Haven",
                Street = "789 Traveller Lane",
                Suburb = "St Kilda",
                Postcode = "3182",
                State = "VIC",
                Region = Region.Metropolitan,
                Phone = "(03) 9345 6789",
                Email = "<EMAIL>",
                Website = "https://www.backpackershaven.com.au",
                MotelType = MotelType.Backpacker,
                Density = Density.High,
                Duration = new List<Duration> { Duration.ShortTerm },
                Inactive = false
            },
            new Motel
            {
                Name = "Regional Rest Stop",
                Street = "321 Highway Drive",
                Suburb = "Ballarat",
                Postcode = "3350",
                State = "VIC",
                Region = Region.Regional,
                Phone = "(03) 5456 7890",
                Email = "<EMAIL>",
                MotelType = MotelType.Motel,
                Density = Density.Low,
                Duration = new List<Duration> { Duration.ShortTerm, Duration.MediumTerm, Duration.LongTerm },
                Inactive = false
            },
            new Motel
            {
                Name = "Caravan Park Lodge",
                Street = "654 Park Avenue",
                Suburb = "Geelong",
                Postcode = "3220",
                State = "VIC",
                Region = Region.Regional,
                Phone = "(03) 5567 8901",
                Email = "<EMAIL>",
                MotelType = MotelType.Caravan,
                Density = Density.Medium,
                Duration = new List<Duration> { Duration.LongTerm, Duration.Permanent },
                Inactive = false
            }
        };

        context.Motels.AddRange(motels);
        await context.SaveChangesAsync();

        // Create sample alerts
        SeedSampleAlertsAsync(context, motels, adminUser, managerUser);

        // Create sample comments
        SeedSampleCommentsAsync(context, motels, adminUser, managerUser);

        // Create sample cautions and suggestions
        SeedSampleCautionsAndSuggestionsAsync(context, motels, adminUser, managerUser);

        // Assign services to motels
        await AssignServicesToMotelsAsync(context, motels);

        await context.SaveChangesAsync();
    }

    private static void SeedSampleAlertsAsync(ApplicationDbContext context, Motel[] motels, ApplicationUser adminUser, ApplicationUser managerUser)
    {
        var alerts = new[]
        {
            new Alert
            {
                UserId = adminUser.Id,
                MotelId = motels[0].Id,
                AlertType = AlertType.Category1,
                Date = DateTime.Today.AddDays(-5),
                Time = new TimeSpan(14, 30, 0),
                Location = "Room 12",
                Description = "Serious incident requiring immediate attention. Police were called.",
                Witnesses = new List<string> { "John Smith", "Jane Doe" },
                ReportedBy = "Front Desk Staff",
                CautionIssued = true
            },
            new Alert
            {
                UserId = managerUser.Id,
                MotelId = motels[1].Id,
                AlertType = AlertType.Category2,
                Date = DateTime.Today.AddDays(-3),
                Time = new TimeSpan(22, 15, 0),
                Location = "Parking Lot",
                Description = "Disturbance in parking area. Resolved by security.",
                Witnesses = new List<string> { "Security Guard" },
                ReportedBy = "Night Manager",
                CautionIssued = false
            },
            new Alert
            {
                UserId = adminUser.Id,
                MotelId = motels[2].Id,
                AlertType = AlertType.Category3,
                Date = DateTime.Today.AddDays(-1),
                Time = new TimeSpan(10, 45, 0),
                Location = "Common Area",
                Description = "Minor altercation between guests. Mediated successfully.",
                Witnesses = new List<string>(),
                ReportedBy = "Duty Manager",
                CautionIssued = false
            }
        };

        context.Alerts.AddRange(alerts);
    }

    private static void SeedSampleCommentsAsync(ApplicationDbContext context, Motel[] motels, ApplicationUser adminUser, ApplicationUser managerUser)
    {
        var comments = new[]
        {
            new Comment
            {
                UserId = managerUser.Id,
                MotelId = motels[0].Id,
                Body = "This property has shown significant improvement in management practices over the past month. Staff training has been effective.",
                ActiveStatus = CommentStatus.Approved,
                ApprovedAt = DateTime.UtcNow.AddDays(-2),
                UserPositionTitle = "Regional Manager",
                UserOrganizationTitle = "Department of Housing"
            },
            new Comment
            {
                UserId = adminUser.Id,
                MotelId = motels[1].Id,
                Body = "Regular inspections show consistent compliance with safety standards. Recommend continued monitoring.",
                ActiveStatus = CommentStatus.ReviewRequired,
                UserPositionTitle = "Senior Inspector",
                UserOrganizationTitle = "Local Council"
            },
            new Comment
            {
                UserId = managerUser.Id,
                MotelId = motels[2].Id,
                Body = "Excellent cooperation from management regarding recent policy changes. Very responsive to feedback.",
                ActiveStatus = CommentStatus.Approved,
                ApprovedAt = DateTime.UtcNow.AddDays(-1),
                Pinned = true,
                UserPositionTitle = "Community Liaison",
                UserOrganizationTitle = "Community Services"
            }
        };

        context.Comments.AddRange(comments);
    }

    private static void SeedSampleCautionsAndSuggestionsAsync(ApplicationDbContext context, Motel[] motels, ApplicationUser adminUser, ApplicationUser managerUser)
    {
        var cautions = new[]
        {
            new Caution
            {
                UserId = adminUser.Id,
                MotelId = motels[0].Id,
                Description = "Fire safety equipment requires immediate attention. Extinguishers need servicing.",
                AuthorisedBy = "Fire Safety Inspector",
                UserOrganization = "Emergency Services",
                UserPositionTitle = "Senior Inspector"
            },
            new Caution
            {
                UserId = managerUser.Id,
                MotelId = motels[3].Id,
                Description = "Security lighting in parking area needs improvement for guest safety.",
                AuthorisedBy = "Security Consultant",
                UserOrganization = "Local Council",
                UserPositionTitle = "Safety Officer"
            }
        };

        var suggestions = new[]
        {
            new Suggestion
            {
                UserId = managerUser.Id,
                MotelId = motels[1].Id,
                Description = "Consider implementing a guest feedback system to improve service quality and identify issues early.",
                AuthorisedBy = "Quality Assurance Manager",
                UserOrganization = "Department of Housing",
                UserPositionTitle = "QA Manager",
                RedCohortFlag = false
            },
            new Suggestion
            {
                UserId = adminUser.Id,
                MotelId = motels[2].Id,
                Description = "High-risk client accommodation requires additional support services and monitoring.",
                AuthorisedBy = "Case Manager",
                UserOrganization = "Community Services",
                UserPositionTitle = "Senior Case Manager",
                RedCohortFlag = true
            }
        };

        context.Cautions.AddRange(cautions);
        context.Suggestions.AddRange(suggestions);
    }

    private static async Task AssignServicesToMotelsAsync(ApplicationDbContext context, Motel[] motels)
    {
        var services = await context.Services.Include(s => s.OrganizationType).ToListAsync();
        var random = new Random();

        foreach (var motel in motels)
        {
            // Assign 3-5 random services to each motel
            var serviceCount = random.Next(3, 6);
            var selectedServices = services.OrderBy(x => random.Next()).Take(serviceCount);

            foreach (var service in selectedServices)
            {
                context.MotelServices.Add(new MotelService
                {
                    MotelId = motel.Id,
                    ServiceId = service.Id
                });
            }
        }
    }
}
