namespace ams_web_mvc.Authorization;

public static class PolicyNames
{
    // Role-based policies
    public const string RequireUser = "RequireUser";
    public const string RequireManager = "RequireManager";
    public const string RequireRestrictedAdmin = "RequireRestrictedAdmin";
    public const string RequireSuperAdmin = "RequireSuperAdmin";
    public const string RequireLeadAdmin = "RequireLeadAdmin";

    // Feature-based policies
    public const string CanManageMotels = "CanManageMotels";
    public const string CanManageAlerts = "CanManageAlerts";
    public const string CanModerateComments = "CanModerateComments";
    public const string CanManageUsers = "CanManageUsers";
    public const string CanApproveNotifications = "CanApproveNotifications";
    public const string CanViewReports = "CanViewReports";
    public const string CanManageSystem = "CanManageSystem";
}
