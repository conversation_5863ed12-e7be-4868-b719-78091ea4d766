using Microsoft.AspNetCore.Authorization;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Authorization.Requirements;

public class RoleRequirement : IAuthorizationRequirement
{
    public UserRole MinimumRole { get; }

    public RoleRequirement(UserRole minimumRole)
    {
        MinimumRole = minimumRole;
    }
}

public class RoleRequirementHandler : AuthorizationHandler<RoleRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, RoleRequirement requirement)
    {
        var roleClaim = context.User.FindFirst(System.Security.Claims.ClaimTypes.Role);
        if (roleClaim == null)
        {
            return Task.CompletedTask;
        }

        if (Enum.TryParse<UserRole>(roleClaim.Value, out var userRole))
        {
            if (userRole >= requirement.MinimumRole)
            {
                context.Succeed(requirement);
            }
        }

        return Task.CompletedTask;
    }
}
