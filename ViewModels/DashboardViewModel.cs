using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.ViewModels;

/// <summary>
/// View model for the dashboard home page
/// </summary>
public class DashboardViewModel
{
    // Statistics
    public int TotalMotels { get; set; }
    public int MotelsWithAlerts { get; set; }
    public int MotelsWithComments { get; set; }
    public int MotelsWithSuggestions { get; set; }
    public int ActiveAlertsCount { get; set; }
    public int PendingCommentsCount { get; set; }

    // Regional breakdown
    public Dictionary<Region, int> MotelsByRegion { get; set; } = new();

    // Recent activity
    public IEnumerable<Alert> RecentAlerts { get; set; } = new List<Alert>();
    public IEnumerable<Comment> PendingComments { get; set; } = new List<Comment>();
    public IEnumerable<string> PopularSearchTerms { get; set; } = new List<string>();

    // Quick access properties
    public double AlertsPercentage => TotalMotels > 0 ? (double)MotelsWithAlerts / TotalMotels * 100 : 0;
    public double CommentsPercentage => TotalMotels > 0 ? (double)MotelsWithComments / TotalMotels * 100 : 0;
    public double SuggestionsPercentage => TotalMotels > 0 ? (double)MotelsWithSuggestions / TotalMotels * 100 : 0;
}
