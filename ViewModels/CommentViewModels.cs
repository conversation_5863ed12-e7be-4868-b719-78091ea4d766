using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace ams_web_mvc.ViewModels;

/// <summary>
/// View model for comment list page
/// </summary>
public class CommentListViewModel
{
    [Display(Name = "Search")]
    public string? SearchTerm { get; set; }

    [Display(Name = "Status")]
    public CommentStatus? SelectedStatus { get; set; }

    public IEnumerable<Comment> Comments { get; set; } = new List<Comment>();

    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }

    // Filter options
    public List<CommentStatus> AvailableStatuses { get; set; } = new();

    // Statistics
    public int PendingCount { get; set; }
    public int ApprovedCount { get; set; }
    public int RejectedCount { get; set; }

    // User permissions
    public bool CanModerate { get; set; }

    // Pagination helpers
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// View model for comment details page
/// </summary>
public class CommentDetailsViewModel
{
    public Comment Comment { get; set; } = new();
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
    public bool CanModerate { get; set; }
}

/// <summary>
/// View model for creating a new comment
/// </summary>
public class CommentCreateViewModel
{
    [Required]
    [Display(Name = "Comment")]
    [StringLength(2000, MinimumLength = 10)]
    public string Content { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Motel")]
    public int? MotelId { get; set; }

    // Available options for dropdowns
    public List<dynamic> AvailableMotels { get; set; } = new();
    public string? MotelName { get; set; } // For display when motelId is pre-selected
}

/// <summary>
/// View model for editing a comment
/// </summary>
public class CommentEditViewModel
{
    public int Id { get; set; }

    [Required]
    [Display(Name = "Comment")]
    [StringLength(2000, MinimumLength = 10)]
    public string Content { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Motel")]
    public int MotelId { get; set; }

    // Available options for dropdowns
    public List<dynamic> AvailableMotels { get; set; } = new();
}

/// <summary>
/// View model for comment moderation dashboard
/// </summary>
public class CommentModerationViewModel
{
    public IEnumerable<Comment> PendingComments { get; set; } = new List<Comment>();
    
    // Statistics
    public int PendingCount { get; set; }
    public int ApprovedCount { get; set; }
    public int RejectedCount { get; set; }
    public int TotalCount => PendingCount + ApprovedCount + RejectedCount;

    // Quick stats
    public double ApprovalRate => TotalCount > 0 ? (double)ApprovedCount / TotalCount * 100 : 0;
    public double RejectionRate => TotalCount > 0 ? (double)RejectedCount / TotalCount * 100 : 0;
}
