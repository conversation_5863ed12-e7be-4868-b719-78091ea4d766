using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace ams_web_mvc.ViewModels;

/// <summary>
/// View model for alert list page
/// </summary>
public class AlertListViewModel
{
    [Display(Name = "Search")]
    public string? SearchTerm { get; set; }

    [Display(Name = "Alert Type")]
    public AlertType? SelectedType { get; set; }

    [Display(Name = "Active Only")]
    public bool? IsActiveFilter { get; set; }

    public IEnumerable<Alert> Alerts { get; set; } = new List<Alert>();

    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }

    // Filter options
    public List<AlertType> AvailableTypes { get; set; } = new();

    // Statistics
    public int ActiveCount { get; set; }
    public int ClosedCount { get; set; }
    public int ArchivedCount { get; set; }

    // Pagination helpers
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// View model for alert details page
/// </summary>
public class AlertDetailsViewModel
{
    public Alert Alert { get; set; } = new();
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
    public bool IsExpired { get; set; }
}

/// <summary>
/// View model for creating a new alert
/// </summary>
public class AlertCreateViewModel
{
    [Required]
    [Display(Name = "Title")]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Description")]
    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Alert Type")]
    public AlertType Type { get; set; }

    [Required]
    [Display(Name = "Motel")]
    public int? MotelId { get; set; }

    [Display(Name = "Expiry Date")]
    [DataType(DataType.DateTime)]
    public DateTime? ExpiryDate { get; set; }

    // Available options for dropdowns
    public List<AlertType> AvailableTypes { get; set; } = new();
    public List<dynamic> AvailableMotels { get; set; } = new();
    public string? MotelName { get; set; } // For display when motelId is pre-selected
}

/// <summary>
/// View model for editing an alert
/// </summary>
public class AlertEditViewModel
{
    public int Id { get; set; }

    [Required]
    [Display(Name = "Title")]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Description")]
    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Alert Type")]
    public AlertType Type { get; set; }

    [Required]
    [Display(Name = "Motel")]
    public int MotelId { get; set; }

    [Display(Name = "Expiry Date")]
    [DataType(DataType.DateTime)]
    public DateTime? ExpiryDate { get; set; }

    // Available options for dropdowns
    public List<AlertType> AvailableTypes { get; set; } = new();
    public List<dynamic> AvailableMotels { get; set; } = new();
}
