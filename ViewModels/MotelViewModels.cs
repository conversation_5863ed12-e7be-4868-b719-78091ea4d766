using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace ams_web_mvc.ViewModels;

/// <summary>
/// View model for motel list page
/// </summary>
public class MotelListViewModel
{
    [Display(Name = "Search")]
    public string? SearchTerm { get; set; }

    [Display(Name = "Region")]
    public Region? SelectedRegion { get; set; }

    public IEnumerable<Motel> Motels { get; set; } = new List<Motel>();

    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }

    // Filter options
    public List<Region> AvailableRegions { get; set; } = new();

    // Pagination helpers
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// View model for motel details page
/// </summary>
public class MotelDetailsViewModel
{
    public Motel Motel { get; set; } = new();
    public int ActiveAlertsCount { get; set; }
    public int PendingCommentsCount { get; set; }
    public IEnumerable<Alert> RecentAlerts { get; set; } = new List<Alert>();
    public IEnumerable<Comment> ApprovedComments { get; set; } = new List<Comment>();
}

/// <summary>
/// View model for creating a new motel
/// </summary>
public class MotelCreateViewModel
{
    [Required]
    [Display(Name = "Motel Name")]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Address")]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Suburb")]
    [StringLength(100)]
    public string Suburb { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Postcode")]
    [StringLength(10)]
    public string Postcode { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Region")]
    public Region Region { get; set; }

    [Display(Name = "Phone")]
    [StringLength(20)]
    public string? Phone { get; set; }

    [Display(Name = "Email")]
    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }

    [Display(Name = "Website")]
    [Url]
    [StringLength(200)]
    public string? Website { get; set; }

    [Display(Name = "Description")]
    [StringLength(1000)]
    public string? Description { get; set; }

    // Available options for dropdowns
    public List<Region> AvailableRegions { get; set; } = new();
}

/// <summary>
/// View model for editing a motel
/// </summary>
public class MotelEditViewModel
{
    public int Id { get; set; }

    [Required]
    [Display(Name = "Motel Name")]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Address")]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Suburb")]
    [StringLength(100)]
    public string Suburb { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Postcode")]
    [StringLength(10)]
    public string Postcode { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Region")]
    public Region Region { get; set; }

    [Display(Name = "Phone")]
    [StringLength(20)]
    public string? Phone { get; set; }

    [Display(Name = "Email")]
    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }

    [Display(Name = "Website")]
    [Url]
    [StringLength(200)]
    public string? Website { get; set; }

    [Display(Name = "Description")]
    [StringLength(1000)]
    public string? Description { get; set; }

    // Available options for dropdowns
    public List<Region> AvailableRegions { get; set; } = new();
}

/// <summary>
/// View model for motel deletion confirmation
/// </summary>
public class MotelDeleteViewModel
{
    public Motel Motel { get; set; } = new();
    public bool HasActiveAlerts { get; set; }
    public int ActiveAlertsCount { get; set; }
    public int PendingCommentsCount { get; set; }
}

/// <summary>
/// View model for managing motel amenities
/// </summary>
public class MotelAmenitiesViewModel
{
    public Motel Motel { get; set; } = new();
    public List<MotelAmenity> CurrentAmenities { get; set; } = new();
    public List<Amenity> AvailableAmenities { get; set; } = new();
}

/// <summary>
/// View model for managing motel services
/// </summary>
public class MotelServicesViewModel
{
    public Motel Motel { get; set; } = new();
    public List<MotelService> CurrentServices { get; set; } = new();
    public List<Service> AvailableServices { get; set; } = new();
}
