using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace ams_web_mvc.ViewModels;

/// <summary>
/// View model for search functionality
/// </summary>
public class SearchViewModel
{
    [Display(Name = "Search Term")]
    public string? SearchTerm { get; set; }

    public IEnumerable<Motel> Results { get; set; } = new List<Motel>();
    public IEnumerable<string> Suggestions { get; set; } = new List<string>();

    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }

    // Pagination helpers
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
    public int StartItem => (PageNumber - 1) * PageSize + 1;
    public int EndItem => Math.Min(PageNumber * PageSize, TotalCount);
}

/// <summary>
/// View model for advanced search
/// </summary>
public class AdvancedSearchViewModel
{
    [Display(Name = "Search Term")]
    public string? SearchTerm { get; set; }

    [Display(Name = "Region")]
    public Region? Region { get; set; }

    [Display(Name = "Amenities")]
    public List<int> SelectedAmenityIds { get; set; } = new();

    [Display(Name = "Services")]
    public List<int> SelectedServiceIds { get; set; } = new();

    [Display(Name = "Exclude Motels")]
    public List<int> ExcludeMotelIds { get; set; } = new();

    [Display(Name = "Sort By")]
    public string SortBy { get; set; } = "Name";

    [Display(Name = "Sort Descending")]
    public bool SortDescending { get; set; } = false;

    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;

    // Available options for dropdowns
    public List<Region> AvailableRegions { get; set; } = new();
    public List<Amenity> AvailableAmenities { get; set; } = new();
    public List<Service> AvailableServices { get; set; } = new();
    public List<Motel> AvailableMotels { get; set; } = new();
}

/// <summary>
/// View model for tab-based views
/// </summary>
public class TabViewModel
{
    public string ActiveTab { get; set; } = "all";
    public IEnumerable<Motel> Results { get; set; } = new List<Motel>();

    // Pagination
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }

    // Tab counts (for badges)
    public int AllCount { get; set; }
    public int AlertsCount { get; set; }
    public int CommentsCount { get; set; }
    public int SuggestionsCount { get; set; }

    // Pagination helpers
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
