using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Configuration;
using ams_web_mvc.Models;
using ams_web_mvc.Models.DTOs;
using ams_web_mvc.Models.Enums;
using System.Security.Cryptography;

namespace ams_web_mvc.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly IJwtService _jwtService;
    private readonly ApplicationSettings _appSettings;

    public AuthenticationService(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        IJwtService jwtService,
        IOptions<ApplicationSettings> appSettings)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _jwtService = jwtService;
        _appSettings = appSettings.Value;
    }

    public async Task<AuthResult> LoginAsync(LoginDto loginDto)
    {
        var user = await _userManager.FindByEmailAsync(loginDto.Email);
        if (user == null || user.IsDiscarded)
        {
            return new AuthResult { Success = false, Errors = { "Invalid email or password." } };
        }

        // Check if password is expired
        if (await IsPasswordExpiredAsync(user))
        {
            return new AuthResult 
            { 
                Success = false, 
                Errors = { "Your password has expired. Please reset your password." },
                User = user
            };
        }

        var result = await _signInManager.PasswordSignInAsync(user, loginDto.Password, loginDto.RememberMe, lockoutOnFailure: true);

        if (result.Succeeded)
        {
            return new AuthResult { Success = true, User = user, Message = "Login successful." };
        }

        if (result.IsLockedOut)
        {
            return new AuthResult { Success = false, Errors = { "Account is locked due to multiple failed login attempts." } };
        }

        return new AuthResult { Success = false, Errors = { "Invalid email or password." } };
    }

    public async Task<AuthResult> LoginWithJwtAsync(LoginDto loginDto)
    {
        var user = await _userManager.FindByEmailAsync(loginDto.Email);
        if (user == null || user.IsDiscarded)
        {
            return new AuthResult { Success = false, Errors = { "Invalid email or password." } };
        }

        if (await IsPasswordExpiredAsync(user))
        {
            return new AuthResult 
            { 
                Success = false, 
                Errors = { "Your password has expired. Please reset your password." },
                User = user
            };
        }

        var passwordValid = await _userManager.CheckPasswordAsync(user, loginDto.Password);
        if (!passwordValid)
        {
            await _userManager.AccessFailedAsync(user);
            return new AuthResult { Success = false, Errors = { "Invalid email or password." } };
        }

        if (await _userManager.IsLockedOutAsync(user))
        {
            return new AuthResult { Success = false, Errors = { "Account is locked due to multiple failed login attempts." } };
        }

        var token = await _jwtService.GenerateTokenAsync(user);
        await _userManager.ResetAccessFailedCountAsync(user);

        return new AuthResult 
        { 
            Success = true, 
            Token = token, 
            User = user, 
            Message = "Login successful." 
        };
    }

    public async Task LogoutAsync()
    {
        await _signInManager.SignOutAsync();
    }

    public async Task<AuthResult> RegisterAsync(RegisterDto registerDto)
    {
        var existingUser = await _userManager.FindByEmailAsync(registerDto.Email);
        if (existingUser != null)
        {
            return new AuthResult { Success = false, Errors = { "User with this email already exists." } };
        }

        var user = new ApplicationUser
        {
            UserName = registerDto.Email,
            Email = registerDto.Email,
            FirstName = registerDto.FirstName,
            LastName = registerDto.LastName,
            Role = registerDto.Role,
            PasswordChangedAt = DateTime.UtcNow
        };

        var result = await _userManager.CreateAsync(user, registerDto.Password);
        if (result.Succeeded)
        {
            return new AuthResult { Success = true, User = user, Message = "User registered successfully." };
        }

        return new AuthResult 
        { 
            Success = false, 
            Errors = result.Errors.Select(e => e.Description).ToList() 
        };
    }

    public async Task<AuthResult> InviteUserAsync(InviteUserDto inviteDto, ApplicationUser invitingUser)
    {
        // Check if inviting user has permission
        if (invitingUser.Role < UserRole.Manager)
        {
            return new AuthResult { Success = false, Errors = { "You don't have permission to invite users." } };
        }

        var existingUser = await _userManager.FindByEmailAsync(inviteDto.Email);
        if (existingUser != null)
        {
            return new AuthResult { Success = false, Errors = { "User with this email already exists." } };
        }

        var invitationToken = GenerateInvitationToken();
        var user = new ApplicationUser
        {
            UserName = inviteDto.Email,
            Email = inviteDto.Email,
            FirstName = inviteDto.FirstName,
            LastName = inviteDto.LastName,
            Role = inviteDto.Role,
            PrimaryContact = inviteDto.PrimaryContact,
            InvitationToken = invitationToken,
            EmailConfirmed = false
        };

        var result = await _userManager.CreateAsync(user);
        if (result.Succeeded)
        {
            // TODO: Send invitation email
            return new AuthResult 
            { 
                Success = true, 
                User = user, 
                Message = "Invitation sent successfully.",
                Token = invitationToken
            };
        }

        return new AuthResult 
        { 
            Success = false, 
            Errors = result.Errors.Select(e => e.Description).ToList() 
        };
    }

    public async Task<AuthResult> AcceptInvitationAsync(AcceptInvitationDto acceptDto)
    {
        var user = await _userManager.Users
            .FirstOrDefaultAsync(u => u.InvitationToken == acceptDto.InvitationToken);

        if (user == null)
        {
            return new AuthResult { Success = false, Errors = { "Invalid invitation token." } };
        }

        // Check if invitation is expired (2 days)
        if (user.CreatedAt.AddDays(_appSettings.InvitationExpiryDays) < DateTime.UtcNow)
        {
            return new AuthResult { Success = false, Errors = { "Invitation has expired." } };
        }

        var token = await _userManager.GeneratePasswordResetTokenAsync(user);
        var result = await _userManager.ResetPasswordAsync(user, token, acceptDto.Password);

        if (result.Succeeded)
        {
            user.InvitationToken = null;
            user.InvitationAcceptedAt = DateTime.UtcNow;
            user.PasswordChangedAt = DateTime.UtcNow;
            user.EmailConfirmed = true;

            await _userManager.UpdateAsync(user);

            return new AuthResult 
            { 
                Success = true, 
                User = user, 
                Message = "Invitation accepted successfully." 
            };
        }

        return new AuthResult 
        { 
            Success = false, 
            Errors = result.Errors.Select(e => e.Description).ToList() 
        };
    }

    public async Task<string> GenerateJwtTokenAsync(ApplicationUser user)
    {
        return await _jwtService.GenerateTokenAsync(user);
    }

    public async Task<bool> ValidatePasswordAsync(ApplicationUser user, string password)
    {
        return await _userManager.CheckPasswordAsync(user, password);
    }

    public Task<bool> IsPasswordExpiredAsync(ApplicationUser user)
    {
        return Task.FromResult(user.IsPasswordExpired(_appSettings.PasswordExpiryDays));
    }

    public async Task<AuthResult> ChangePasswordAsync(string userId, ChangePasswordDto changePasswordDto)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            return new AuthResult { Success = false, Errors = { "User not found." } };
        }

        var result = await _userManager.ChangePasswordAsync(user, changePasswordDto.CurrentPassword, changePasswordDto.NewPassword);
        if (result.Succeeded)
        {
            user.PasswordChangedAt = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            return new AuthResult { Success = true, Message = "Password changed successfully." };
        }

        return new AuthResult 
        { 
            Success = false, 
            Errors = result.Errors.Select(e => e.Description).ToList() 
        };
    }

    public async Task<AuthResult> ResetPasswordAsync(ResetPasswordDto resetPasswordDto)
    {
        var user = await _userManager.FindByEmailAsync(resetPasswordDto.Email);
        if (user == null)
        {
            // Don't reveal that user doesn't exist
            return new AuthResult { Success = true, Message = "If the email exists, a password reset link has been sent." };
        }

        var result = await _userManager.ResetPasswordAsync(user, resetPasswordDto.Token, resetPasswordDto.NewPassword);
        if (result.Succeeded)
        {
            user.PasswordChangedAt = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            return new AuthResult { Success = true, Message = "Password reset successfully." };
        }

        return new AuthResult 
        { 
            Success = false, 
            Errors = result.Errors.Select(e => e.Description).ToList() 
        };
    }

    private string GenerateInvitationToken()
    {
        var randomBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }
}
