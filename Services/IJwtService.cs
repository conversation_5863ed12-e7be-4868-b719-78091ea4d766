using ams_web_mvc.Models;
using System.Security.Claims;

namespace ams_web_mvc.Services;

public interface IJwtService
{
    Task<string> GenerateTokenAsync(ApplicationUser user);
    ClaimsPrincipal? ValidateToken(string token);
    Task<string> GenerateRefreshTokenAsync();
    Task<bool> ValidateRefreshTokenAsync(string userId, string refreshToken);
    Task RevokeTokenAsync(string userId);
    Task RevokeAllTokensAsync(string userId);
}
