using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;
using ams_web_mvc.Services.Interfaces;

namespace ams_web_mvc.Services.Implementations;

/// <summary>
/// Service implementation for advanced search and filtering capabilities
/// </summary>
public class SearchService : ISearchService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<SearchService> _logger;
    private readonly IMemoryCache _cache;
    private const int CACHE_DURATION_MINUTES = 15;

    public SearchService(
        IUnitOfWork unitOfWork, 
        ILogger<SearchService> logger,
        IMemoryCache cache)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
    }

    #region Full-Text Search

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> FullTextSearchAsync(
        string searchTerm,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetAllMotelsAsync(pageNumber, pageSize, cancellationToken);
            }

            _logger.LogInformation("Performing full-text search for: {SearchTerm}", searchTerm);

            // Use the repository's search functionality
            var results = await _unitOfWork.Motels.SearchAsync(
                searchTerm: searchTerm,
                pageNumber: pageNumber,
                pageSize: pageSize,
                cancellationToken: cancellationToken);

            // Log search for analytics
            await LogSearchTermAsync(searchTerm, cancellationToken);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing full-text search for: {SearchTerm}", searchTerm);
            throw;
        }
    }

    #endregion

    #region Advanced Search

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchMotelsAsync(
        MotelSearchCriteria criteria,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Performing advanced motel search with criteria: {@Criteria}", criteria);

            var results = await _unitOfWork.Motels.SearchAsync(
                searchTerm: criteria.SearchTerm,
                region: criteria.Region,
                amenityIds: criteria.AmenityIds,
                serviceIds: criteria.ServiceIds,
                excludeMotelIds: criteria.ExcludeMotelIds,
                pageNumber: criteria.PageNumber,
                pageSize: criteria.PageSize,
                cancellationToken: cancellationToken);

            // Apply additional filtering if needed
            var filteredResults = await ApplyAdditionalFiltersAsync(results.Items, criteria, cancellationToken);

            // Log search if there's a search term
            if (!string.IsNullOrWhiteSpace(criteria.SearchTerm))
            {
                await LogSearchTermAsync(criteria.SearchTerm, cancellationToken);
            }

            return (filteredResults, results.TotalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing advanced motel search");
            throw;
        }
    }

    #endregion

    #region Search Suggestions

    public async Task<IEnumerable<string>> GetSearchSuggestionsAsync(
        string searchTerm,
        int maxResults = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return Enumerable.Empty<string>();
            }

            var cacheKey = $"search_suggestions_{searchTerm.ToLower()}_{maxResults}";
            
            if (_cache.TryGetValue(cacheKey, out IEnumerable<string>? cachedSuggestions))
            {
                return cachedSuggestions ?? Enumerable.Empty<string>();
            }

            var suggestions = new List<string>();

            // Get motel name suggestions
            var motelNames = await _unitOfWork.Motels.SearchByNameAsync(searchTerm, cancellationToken);
            suggestions.AddRange(motelNames.Take(maxResults / 2).Select(m => m.Name));

            // Get suburb suggestions
            var suburbMotels = await _unitOfWork.Motels.SearchBySuburbAsync(searchTerm, cancellationToken);
            suggestions.AddRange(suburbMotels.Take(maxResults / 2).Select(m => m.Suburb).Distinct());

            var result = suggestions.Distinct().Take(maxResults).ToList();

            // Cache for 5 minutes
            _cache.Set(cacheKey, result, TimeSpan.FromMinutes(5));

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search suggestions for: {SearchTerm}", searchTerm);
            return Enumerable.Empty<string>();
        }
    }

    #endregion

    #region Location-Based Search

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchByLocationAsync(
        string? suburb = null,
        string? postcode = null,
        Region? region = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Searching by location - Suburb: {Suburb}, Postcode: {Postcode}, Region: {Region}", 
                suburb, postcode, region);

            var results = new List<Motel>();

            if (!string.IsNullOrWhiteSpace(suburb))
            {
                var suburbResults = await _unitOfWork.Motels.SearchBySuburbAsync(suburb, cancellationToken);
                results.AddRange(suburbResults);
            }

            if (!string.IsNullOrWhiteSpace(postcode))
            {
                var postcodeResults = await _unitOfWork.Motels.SearchByPostcodeAsync(postcode, cancellationToken);
                results.AddRange(postcodeResults);
            }

            if (region.HasValue)
            {
                var regionResults = await _unitOfWork.Motels.GetByRegionAsync(region.Value, cancellationToken);
                results.AddRange(regionResults);
            }

            // Remove duplicates and apply pagination
            var distinctResults = results.Distinct().ToList();
            var totalCount = distinctResults.Count;
            var pagedResults = distinctResults
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            return (pagedResults, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching by location");
            throw;
        }
    }

    #endregion

    #region Amenity and Service Filtering

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchWithAmenitiesAsync(
        List<int> amenityIds,
        bool requireAll = false,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Searching with amenities: {AmenityIds}, RequireAll: {RequireAll}", 
                string.Join(",", amenityIds), requireAll);

            var results = await _unitOfWork.Motels.GetByAmenitiesAsync(amenityIds, requireAll, cancellationToken);

            // Apply search term filter if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                results = results.Where(m => 
                    m.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    m.Suburb.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    m.Address.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
            }

            var resultsList = results.ToList();
            var totalCount = resultsList.Count;
            var pagedResults = resultsList
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            return (pagedResults, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching with amenities");
            throw;
        }
    }

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchWithServicesAsync(
        List<int> serviceIds,
        bool requireAll = false,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Searching with services: {ServiceIds}, RequireAll: {RequireAll}", 
                string.Join(",", serviceIds), requireAll);

            var results = await _unitOfWork.Motels.GetByServicesAsync(serviceIds, requireAll, cancellationToken);

            // Apply search term filter if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                results = results.Where(m => 
                    m.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    m.Suburb.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    m.Address.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
            }

            var resultsList = results.ToList();
            var totalCount = resultsList.Count;
            var pagedResults = resultsList
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            return (pagedResults, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching with services");
            throw;
        }
    }

    #endregion

    #region Exclusion Search

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchWithExclusionsAsync(
        List<int> excludeMotelIds,
        string? searchTerm = null,
        Region? region = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Searching with exclusions: {ExcludeIds}", string.Join(",", excludeMotelIds));

            var results = await _unitOfWork.Motels.SearchAsync(
                searchTerm: searchTerm,
                region: region,
                excludeMotelIds: excludeMotelIds,
                pageNumber: pageNumber,
                pageSize: pageSize,
                cancellationToken: cancellationToken);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching with exclusions");
            throw;
        }
    }

    #endregion

    #region Tab-Based Searches

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> GetMotelsWithAlertsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"motels_with_alerts_{pageNumber}_{pageSize}";

            if (_cache.TryGetValue(cacheKey, out (IEnumerable<Motel>, int)? cachedResult))
            {
                return cachedResult ?? (Enumerable.Empty<Motel>(), 0);
            }

            var allMotels = await _unitOfWork.Motels.GetAllAsync(cancellationToken);
            var motelsWithAlerts = new List<Motel>();

            foreach (var motel in allMotels)
            {
                var hasAlerts = await _unitOfWork.Motels.HasActiveAlertsAsync(motel.Id, cancellationToken);
                if (hasAlerts)
                {
                    motelsWithAlerts.Add(motel);
                }
            }

            var totalCount = motelsWithAlerts.Count;
            var pagedResults = motelsWithAlerts
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            var result = (pagedResults, totalCount);
            _cache.Set(cacheKey, result, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels with alerts");
            throw;
        }
    }

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> GetMotelsWithSuggestionsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"motels_with_suggestions_{pageNumber}_{pageSize}";

            if (_cache.TryGetValue(cacheKey, out (IEnumerable<Motel>, int)? cachedResult))
            {
                return cachedResult ?? (Enumerable.Empty<Motel>(), 0);
            }

            var allMotels = await _unitOfWork.Motels.GetAllAsync(cancellationToken);
            var motelsWithSuggestions = new List<Motel>();

            foreach (var motel in allMotels)
            {
                var suggestionCount = await _unitOfWork.Suggestions.GetCountByMotelIdAsync(motel.Id, cancellationToken);
                if (suggestionCount > 0)
                {
                    motelsWithSuggestions.Add(motel);
                }
            }

            var totalCount = motelsWithSuggestions.Count;
            var pagedResults = motelsWithSuggestions
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            var result = (pagedResults, totalCount);
            _cache.Set(cacheKey, result, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels with suggestions");
            throw;
        }
    }

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> GetMotelsWithCommentsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"motels_with_comments_{pageNumber}_{pageSize}";

            if (_cache.TryGetValue(cacheKey, out (IEnumerable<Motel>, int)? cachedResult))
            {
                return cachedResult ?? (Enumerable.Empty<Motel>(), 0);
            }

            var allMotels = await _unitOfWork.Motels.GetAllAsync(cancellationToken);
            var motelsWithComments = new List<Motel>();

            foreach (var motel in allMotels)
            {
                var commentCount = await _unitOfWork.Comments.GetCountByMotelAsync(motel.Id, cancellationToken);
                if (commentCount > 0)
                {
                    motelsWithComments.Add(motel);
                }
            }

            var totalCount = motelsWithComments.Count;
            var pagedResults = motelsWithComments
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            var result = (pagedResults, totalCount);
            _cache.Set(cacheKey, result, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motels with comments");
            throw;
        }
    }

    #endregion

    #region Statistics and Analytics

    public async Task<SearchStatistics> GetSearchStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            const string cacheKey = "search_statistics";

            if (_cache.TryGetValue(cacheKey, out SearchStatistics? cachedStats))
            {
                return cachedStats ?? new SearchStatistics();
            }

            var statistics = new SearchStatistics
            {
                TotalMotels = await _unitOfWork.Motels.CountAsync(cancellationToken),
                MotelsByRegion = await _unitOfWork.Motels.GetCountsByRegionAsync(cancellationToken),
                LastUpdated = DateTime.UtcNow
            };

            // Count motels with alerts, comments, and suggestions
            var allMotels = await _unitOfWork.Motels.GetAllAsync(cancellationToken);
            var motelsWithAlerts = 0;
            var motelsWithComments = 0;
            var motelsWithSuggestions = 0;

            foreach (var motel in allMotels)
            {
                if (await _unitOfWork.Motels.HasActiveAlertsAsync(motel.Id, cancellationToken))
                    motelsWithAlerts++;

                if (await _unitOfWork.Comments.GetCountByMotelAsync(motel.Id, cancellationToken) > 0)
                    motelsWithComments++;

                if (await _unitOfWork.Suggestions.GetCountByMotelIdAsync(motel.Id, cancellationToken) > 0)
                    motelsWithSuggestions++;
            }

            statistics.MotelsWithAlerts = motelsWithAlerts;
            statistics.MotelsWithComments = motelsWithComments;
            statistics.MotelsWithSuggestions = motelsWithSuggestions;

            // Cache for 30 minutes
            _cache.Set(cacheKey, statistics, TimeSpan.FromMinutes(30));

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search statistics");
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetPopularSearchTermsAsync(
        int maxResults = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            const string cacheKey = "popular_search_terms";

            if (_cache.TryGetValue(cacheKey, out IEnumerable<string>? cachedTerms))
            {
                return cachedTerms ?? Enumerable.Empty<string>();
            }

            // In a real implementation, you would track search terms in a database
            // For now, return some common terms based on motel names and suburbs
            var motelNames = await _unitOfWork.Motels.GetAllAsync(cancellationToken);
            var popularTerms = motelNames
                .SelectMany(m => m.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries))
                .Where(term => term.Length > 3)
                .GroupBy(term => term.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(maxResults)
                .Select(g => g.Key)
                .ToList();

            // Cache for 1 hour
            _cache.Set(cacheKey, popularTerms, TimeSpan.FromHours(1));

            return popularTerms;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular search terms");
            return Enumerable.Empty<string>();
        }
    }

    #endregion

    #region Private Helper Methods

    private async Task<IEnumerable<Motel>> ApplyAdditionalFiltersAsync(
        IEnumerable<Motel> motels,
        MotelSearchCriteria criteria,
        CancellationToken cancellationToken)
    {
        var filteredMotels = motels.ToList();

        // Filter by suburb if specified
        if (!string.IsNullOrWhiteSpace(criteria.Suburb))
        {
            filteredMotels = filteredMotels
                .Where(m => m.Suburb.Contains(criteria.Suburb, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        // Filter by postcode if specified
        if (!string.IsNullOrWhiteSpace(criteria.Postcode))
        {
            filteredMotels = filteredMotels
                .Where(m => m.Postcode == criteria.Postcode)
                .ToList();
        }

        // Apply sorting
        filteredMotels = ApplySorting(filteredMotels, criteria.SortBy, criteria.SortDescending);

        return filteredMotels;
    }

    private List<Motel> ApplySorting(List<Motel> motels, string? sortBy, bool descending)
    {
        return sortBy?.ToLower() switch
        {
            "name" => descending ? motels.OrderByDescending(m => m.Name).ToList() : motels.OrderBy(m => m.Name).ToList(),
            "suburb" => descending ? motels.OrderByDescending(m => m.Suburb).ToList() : motels.OrderBy(m => m.Suburb).ToList(),
            "postcode" => descending ? motels.OrderByDescending(m => m.Postcode).ToList() : motels.OrderBy(m => m.Postcode).ToList(),
            "region" => descending ? motels.OrderByDescending(m => m.Region).ToList() : motels.OrderBy(m => m.Region).ToList(),
            "created" => descending ? motels.OrderByDescending(m => m.CreatedAt).ToList() : motels.OrderBy(m => m.CreatedAt).ToList(),
            "updated" => descending ? motels.OrderByDescending(m => m.UpdatedAt).ToList() : motels.OrderBy(m => m.UpdatedAt).ToList(),
            _ => motels.OrderBy(m => m.Name).ToList()
        };
    }

    private async Task<(IEnumerable<Motel> Items, int TotalCount)> GetAllMotelsAsync(
        int pageNumber,
        int pageSize,
        CancellationToken cancellationToken)
    {
        var allMotels = await _unitOfWork.Motels.GetAllAsync(cancellationToken);
        var totalCount = allMotels.Count();
        var pagedResults = allMotels
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize);

        return (pagedResults, totalCount);
    }

    private async Task LogSearchTermAsync(string searchTerm, CancellationToken cancellationToken)
    {
        try
        {
            // In a real implementation, you would log search terms to a database for analytics
            _logger.LogInformation("Search term logged: {SearchTerm}", searchTerm);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log search term: {SearchTerm}", searchTerm);
        }
    }

    #endregion
}
