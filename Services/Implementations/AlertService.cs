using Microsoft.Extensions.Logging;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;
using ams_web_mvc.Services.Interfaces;

namespace ams_web_mvc.Services.Implementations;

/// <summary>
/// Service implementation for Alert business logic and workflow management
/// </summary>
public class AlertService : IAlertService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AlertService> _logger;

    public AlertService(IUnitOfWork unitOfWork, ILogger<AlertService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region Basic CRUD Operations

    public async Task<Alert?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetByIdAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alert with ID {AlertId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetAllAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all alerts");
            throw;
        }
    }

    public async Task<Alert> CreateAsync(Alert alert, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate business rules
            await ValidateAlertAsync(alert, cancellationToken);

            // Set audit fields
            alert.UserId = userId;
            alert.CreatedAt = DateTime.UtcNow;
            alert.UpdatedAt = DateTime.UtcNow;

            // Add to repository
            await _unitOfWork.Alerts.AddAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created new alert: {AlertTitle} (ID: {AlertId}) by user {UserId}", alert.Title, alert.Id, userId);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert: {AlertTitle} by user {UserId}", alert.Title, userId);
            throw;
        }
    }

    public async Task<Alert> UpdateAsync(Alert alert, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate business rules
            await ValidateAlertAsync(alert, cancellationToken);

            // Check if alert exists and user can edit
            var canEdit = await CanUserEditAsync(alert.Id, userId, cancellationToken);
            if (!canEdit)
            {
                throw new UnauthorizedAccessException("User is not authorized to edit this alert.");
            }

            // Update audit fields
            alert.UpdatedAt = DateTime.UtcNow;

            // Update in repository
            await _unitOfWork.Alerts.UpdateAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated alert: {AlertTitle} (ID: {AlertId}) by user {UserId}", alert.Title, alert.Id, userId);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating alert: {AlertTitle} (ID: {AlertId}) by user {UserId}", alert.Title, alert.Id, userId);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(id, cancellationToken);
            if (alert == null)
            {
                return false;
            }

            // Check if user can delete
            var canDelete = await CanUserDeleteAsync(id, userId, cancellationToken);
            if (!canDelete)
            {
                throw new UnauthorizedAccessException("User is not authorized to delete this alert.");
            }

            // Soft delete
            await _unitOfWork.Alerts.SoftDeleteAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted alert: {AlertTitle} (ID: {AlertId}) by user {UserId}", alert.Title, id, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting alert with ID {AlertId} by user {UserId}", id, userId);
            throw;
        }
    }

    #endregion

    #region Alert Lifecycle Management

    public async Task<Alert> CloseAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null)
            {
                throw new ArgumentException($"Alert with ID {alertId} not found.");
            }

            alert.IsClosed = true;
            alert.ClosedAt = DateTime.UtcNow;
            alert.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Alerts.UpdateAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Closed alert {AlertId} by user {UserId}", alertId, userId);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing alert {AlertId} by user {UserId}", alertId, userId);
            throw;
        }
    }

    public async Task<Alert> ReopenAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null)
            {
                throw new ArgumentException($"Alert with ID {alertId} not found.");
            }

            alert.IsClosed = false;
            alert.ClosedAt = null;
            alert.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Alerts.UpdateAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Reopened alert {AlertId} by user {UserId}", alertId, userId);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reopening alert {AlertId} by user {UserId}", alertId, userId);
            throw;
        }
    }

    public async Task<Alert> DiscardAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null)
            {
                throw new ArgumentException($"Alert with ID {alertId} not found.");
            }

            alert.IsDiscarded = true;
            alert.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Alerts.UpdateAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Discarded alert {AlertId} by user {UserId}", alertId, userId);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error discarding alert {AlertId} by user {UserId}", alertId, userId);
            throw;
        }
    }

    public async Task<Alert> RestoreAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null)
            {
                throw new ArgumentException($"Alert with ID {alertId} not found.");
            }

            alert.IsDiscarded = false;
            alert.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Alerts.UpdateAsync(alert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Restored alert {AlertId} by user {UserId}", alertId, userId);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring alert {AlertId} by user {UserId}", alertId, userId);
            throw;
        }
    }

    #endregion

    #region Filtering and Search

    public async Task<IEnumerable<Alert>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetByMotelIdAsync(motelId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alerts for motel {MotelId}", motelId);
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetByReporterIdAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alerts for user {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetByAlertTypeAsync(AlertType alertType, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetByTypeAsync(alertType, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alerts by type {AlertType}", alertType);
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.FindAsync(a => !a.IsClosed && !a.IsDiscarded, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active alerts");
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetClosedAlertsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.FindAsync(a => a.IsClosed, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving closed alerts");
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetArchivedAlertsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.FindAsync(a => a.IsDiscarded, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving archived alerts");
            throw;
        }
    }

    #endregion

    #region Date-based Queries

    public async Task<IEnumerable<Alert>> GetRecentAlertsAsync(int days = 30, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetRecentAlertsAsync(days, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving recent alerts for {Days} days", days);
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetByDateRangeAsync(startDate, endDate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alerts by date range {StartDate} to {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<IEnumerable<Alert>> GetExpiredAlertsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetExpiredAlertsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving expired alerts");
            throw;
        }
    }

    #endregion

    #region Advanced Search

    public async Task<(IEnumerable<Alert> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null, AlertType? alertType = null, int? motelId = null, string? userId = null,
        DateTime? startDate = null, DateTime? endDate = null, bool? isActive = null, bool? isClosed = null,
        bool? isArchived = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.SearchAsync(
                searchTerm, null, alertType, null, motelId, userId,
                startDate, endDate, pageNumber, pageSize, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing advanced alert search");
            throw;
        }
    }

    #endregion

    #region Related Data Loading

    public async Task<Alert?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetWithMotelAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alert with motel: {AlertId}", id);
            throw;
        }
    }

    public async Task<Alert?> GetWithUserAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetWithReporterAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alert with user: {AlertId}", id);
            throw;
        }
    }

    public async Task<Alert?> GetWithAllDataAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.GetWithAllRelatedDataAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving alert with all data: {AlertId}", id);
            throw;
        }
    }

    #endregion

    #region Business Logic

    public async Task<bool> CanUserEditAsync(int alertId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null) return false;

            // User can edit their own alerts if not closed
            return alert.UserId == userId && !alert.IsClosed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking edit permissions for alert {AlertId} by user {UserId}", alertId, userId);
            throw;
        }
    }

    public async Task<bool> CanUserDeleteAsync(int alertId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null) return false;

            // User can delete their own alerts
            return alert.UserId == userId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking delete permissions for alert {AlertId} by user {UserId}", alertId, userId);
            throw;
        }
    }

    public async Task<bool> IsExpiredAsync(int alertId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _unitOfWork.Alerts.GetByIdAsync(alertId, cancellationToken);
            if (alert == null) return false;

            return alert.ExpiryDate.HasValue && alert.ExpiryDate < DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if alert {AlertId} is expired", alertId);
            throw;
        }
    }

    #endregion

    #region Validation

    public async Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Alerts.ExistsAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if alert exists: {AlertId}", id);
            throw;
        }
    }

    #endregion

    #region Private Helper Methods

    private async Task ValidateAlertAsync(Alert alert, CancellationToken cancellationToken)
    {
        // Validate required fields
        if (string.IsNullOrWhiteSpace(alert.Title))
        {
            throw new ArgumentException("Alert title is required.");
        }

        if (string.IsNullOrWhiteSpace(alert.Description))
        {
            throw new ArgumentException("Alert description is required.");
        }

        if (alert.MotelId <= 0)
        {
            throw new ArgumentException("Valid motel ID is required.");
        }

        // Validate motel exists
        var motelExists = await _unitOfWork.Motels.ExistsAsync(alert.MotelId, cancellationToken);
        if (!motelExists)
        {
            throw new ArgumentException($"Motel with ID {alert.MotelId} does not exist.");
        }

        // Additional business rule validations can be added here
    }

    #endregion
}
