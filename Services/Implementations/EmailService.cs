using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Net.Mail;
using System.Net;
using System.Text;
using ams_web_mvc.Models;
using ams_web_mvc.Services.Interfaces;
using ams_web_mvc.Configuration;

namespace ams_web_mvc.Services.Implementations;

/// <summary>
/// Service implementation for email delivery and template management
/// </summary>
public class EmailService : IEmailService
{
    private readonly EmailSettings _emailSettings;
    private readonly ILogger<EmailService> _logger;
    private readonly SmtpClient _smtpClient;

    public EmailService(
        IOptions<EmailSettings> emailSettings,
        ILogger<EmailService> logger)
    {
        _emailSettings = emailSettings?.Value ?? throw new ArgumentNullException(nameof(emailSettings));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _smtpClient = CreateSmtpClient();
    }

    #region Basic Email Sending

    public async Task<bool> SendEmailAsync(
        string to,
        string subject,
        string body,
        bool isHtml = true,
        CancellationToken cancellationToken = default)
    {
        return await SendEmailAsync(new List<string> { to }, subject, body, isHtml, cancellationToken: cancellationToken);
    }

    public async Task<bool> SendEmailAsync(
        List<string> to,
        string subject,
        string body,
        bool isHtml = true,
        List<string>? cc = null,
        List<string>? bcc = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var message = new MailMessage();
            
            // Set sender
            message.From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName);
            
            // Add recipients
            foreach (var recipient in to)
            {
                if (await ValidateEmailAddressAsync(recipient))
                {
                    message.To.Add(recipient);
                }
            }

            // Add CC recipients
            if (cc != null)
            {
                foreach (var ccRecipient in cc)
                {
                    if (await ValidateEmailAddressAsync(ccRecipient))
                    {
                        message.CC.Add(ccRecipient);
                    }
                }
            }

            // Add BCC recipients
            if (bcc != null)
            {
                foreach (var bccRecipient in bcc)
                {
                    if (await ValidateEmailAddressAsync(bccRecipient))
                    {
                        message.Bcc.Add(bccRecipient);
                    }
                }
            }

            if (message.To.Count == 0)
            {
                _logger.LogWarning("No valid recipients found for email: {Subject}", subject);
                return false;
            }

            message.Subject = subject;
            message.Body = body;
            message.IsBodyHtml = isHtml;
            message.BodyEncoding = Encoding.UTF8;
            message.SubjectEncoding = Encoding.UTF8;

            await _smtpClient.SendMailAsync(message, cancellationToken);

            _logger.LogInformation("Email sent successfully to {RecipientCount} recipients: {Subject}", 
                message.To.Count, subject);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email: {Subject}", subject);
            return false;
        }
    }

    #endregion

    #region Template-Based Email Sending

    public async Task<bool> SendTemplatedEmailAsync(
        string to,
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default)
    {
        return await SendTemplatedEmailAsync(new List<string> { to }, templateName, templateData, cancellationToken);
    }

    public async Task<bool> SendTemplatedEmailAsync(
        List<string> to,
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await GetEmailTemplateAsync(templateName);
            if (template == null)
            {
                _logger.LogError("Email template not found: {TemplateName}", templateName);
                return false;
            }

            var subject = RenderTemplate(template.Subject, templateData);
            var body = RenderTemplate(template.Body, templateData);

            return await SendEmailAsync(to, subject, body, template.IsHtml, cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending templated email: {TemplateName}", templateName);
            return false;
        }
    }

    #endregion

    #region Notification-Specific Emails

    public async Task<bool> SendNotificationEmailAsync(
        Notification notification,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(notification.RecipientId))
            {
                _logger.LogWarning("Notification has no recipient: {NotificationId}", notification.Id);
                return false;
            }

            // Get recipient email (this would typically come from user service)
            var recipientEmail = await GetUserEmailAsync(notification.RecipientId);
            if (string.IsNullOrEmpty(recipientEmail))
            {
                _logger.LogWarning("No email found for user: {UserId}", notification.RecipientId);
                return false;
            }

            var templateName = GetNotificationTemplateName(notification.Type);
            var templateData = new
            {
                Title = notification.Title,
                Message = notification.Message,
                Type = notification.Type.ToString(),
                CreatedAt = notification.CreatedAt,
                ApplicationUrl = _emailSettings.ApplicationUrl
            };

            return await SendTemplatedEmailAsync(recipientEmail, templateName, templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification email: {NotificationId}", notification.Id);
            return false;
        }
    }

    public async Task<bool> SendAlertNotificationAsync(
        Alert alert,
        ApplicationUser recipient,
        string? customMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                AlertTitle = alert.Title,
                AlertDescription = alert.Description,
                AlertType = alert.Type.ToString(),
                MotelName = alert.Motel?.Name ?? "Unknown Motel",
                CustomMessage = customMessage,
                RecipientName = recipient.FirstName,
                CreatedAt = alert.CreatedAt,
                ExpiryDate = alert.ExpiryDate,
                ApplicationUrl = _emailSettings.ApplicationUrl,
                AlertUrl = $"{_emailSettings.ApplicationUrl}/Alert/Details/{alert.Id}"
            };

            return await SendTemplatedEmailAsync(recipient.Email!, "AlertNotification", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending alert notification email for alert: {AlertId}", alert.Id);
            return false;
        }
    }

    public async Task<bool> SendCommentNotificationAsync(
        Comment comment,
        ApplicationUser recipient,
        string? customMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                CommentContent = comment.Content,
                CommentStatus = comment.Status.ToString(),
                MotelName = comment.Motel?.Name ?? "Unknown Motel",
                CustomMessage = customMessage,
                RecipientName = recipient.FirstName,
                CreatedAt = comment.CreatedAt,
                ApplicationUrl = _emailSettings.ApplicationUrl,
                CommentUrl = $"{_emailSettings.ApplicationUrl}/Comment/Details/{comment.Id}"
            };

            return await SendTemplatedEmailAsync(recipient.Email!, "CommentNotification", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending comment notification email for comment: {CommentId}", comment.Id);
            return false;
        }
    }

    #endregion

    #region System Emails

    public async Task<bool> SendWelcomeEmailAsync(
        ApplicationUser user,
        string? invitationMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                UserName = user.FirstName,
                Email = user.Email,
                InvitationMessage = invitationMessage,
                ApplicationUrl = _emailSettings.ApplicationUrl,
                LoginUrl = $"{_emailSettings.ApplicationUrl}/Account/Login"
            };

            return await SendTemplatedEmailAsync(user.Email!, "Welcome", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending welcome email to user: {UserId}", user.Id);
            return false;
        }
    }

    public async Task<bool> SendPasswordResetEmailAsync(
        ApplicationUser user,
        string resetToken,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                UserName = user.FirstName,
                ResetToken = resetToken,
                ApplicationUrl = _emailSettings.ApplicationUrl,
                ResetUrl = $"{_emailSettings.ApplicationUrl}/Account/ResetPassword?token={resetToken}&email={user.Email}"
            };

            return await SendTemplatedEmailAsync(user.Email!, "PasswordReset", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password reset email to user: {UserId}", user.Id);
            return false;
        }
    }

    public async Task<bool> SendAccountLockedEmailAsync(
        ApplicationUser user,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                UserName = user.FirstName,
                LockoutEnd = user.LockoutEnd,
                ApplicationUrl = _emailSettings.ApplicationUrl,
                SupportEmail = _emailSettings.SupportEmail
            };

            return await SendTemplatedEmailAsync(user.Email!, "AccountLocked", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending account locked email to user: {UserId}", user.Id);
            return false;
        }
    }

    #endregion

    #region Email Validation and Utilities

    public async Task<bool> ValidateEmailAddressAsync(string email)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            var addr = new MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> IsEmailDeliverable(string email)
    {
        // Basic implementation - in production, you might want to use a service
        // like SendGrid's email validation API or similar
        return await ValidateEmailAddressAsync(email);
    }

    public async Task<EmailDeliveryStatus> GetDeliveryStatusAsync(string messageId)
    {
        // This would typically integrate with your email provider's API
        // For now, return unknown status
        await Task.CompletedTask;
        return EmailDeliveryStatus.Unknown;
    }

    #endregion

    #region Template Management

    public async Task<string> RenderTemplateAsync(
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await GetEmailTemplateAsync(templateName);
            if (template == null)
            {
                throw new InvalidOperationException($"Template not found: {templateName}");
            }

            return RenderTemplate(template.Body, templateData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering template: {TemplateName}", templateName);
            throw;
        }
    }

    public async Task<bool> ValidateTemplateAsync(
        string templateName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await GetEmailTemplateAsync(templateName);
            return template != null && !string.IsNullOrEmpty(template.Body);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating template: {TemplateName}", templateName);
            return false;
        }
    }

    #endregion

    #region Digest Emails

    public async Task<bool> SendDailyDigestAsync(
        ApplicationUser user,
        DigestData digestData,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                UserName = user.FirstName,
                DigestData = digestData,
                PeriodStart = digestData.PeriodStart,
                PeriodEnd = digestData.PeriodEnd,
                NewAlertsCount = digestData.NewAlerts.Count,
                NewCommentsCount = digestData.NewComments.Count,
                UnreadNotificationsCount = digestData.UnreadNotifications.Count,
                ApplicationUrl = _emailSettings.ApplicationUrl
            };

            return await SendTemplatedEmailAsync(user.Email!, "DailyDigest", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending daily digest to user: {UserId}", user.Id);
            return false;
        }
    }

    public async Task<bool> SendWeeklyDigestAsync(
        ApplicationUser user,
        DigestData digestData,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                UserName = user.FirstName,
                DigestData = digestData,
                PeriodStart = digestData.PeriodStart,
                PeriodEnd = digestData.PeriodEnd,
                ApplicationUrl = _emailSettings.ApplicationUrl
            };

            return await SendTemplatedEmailAsync(user.Email!, "WeeklyDigest", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending weekly digest to user: {UserId}", user.Id);
            return false;
        }
    }

    public async Task<bool> SendMonthlyDigestAsync(
        ApplicationUser user,
        DigestData digestData,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templateData = new
            {
                UserName = user.FirstName,
                DigestData = digestData,
                PeriodStart = digestData.PeriodStart,
                PeriodEnd = digestData.PeriodEnd,
                ApplicationUrl = _emailSettings.ApplicationUrl
            };

            return await SendTemplatedEmailAsync(user.Email!, "MonthlyDigest", templateData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending monthly digest to user: {UserId}", user.Id);
            return false;
        }
    }

    #endregion

    #region Bulk Operations

    public async Task<BulkEmailResult> SendBulkEmailAsync(
        List<string> recipients,
        string subject,
        string body,
        bool isHtml = true,
        CancellationToken cancellationToken = default)
    {
        var result = new BulkEmailResult
        {
            TotalEmails = recipients.Count
        };

        var startTime = DateTime.UtcNow;

        foreach (var recipient in recipients)
        {
            try
            {
                var success = await SendEmailAsync(recipient, subject, body, isHtml, cancellationToken);
                if (success)
                {
                    result.SuccessfulEmails++;
                }
                else
                {
                    result.FailedEmails++;
                    result.FailedRecipients.Add(recipient);
                    result.ErrorMessages.Add($"Failed to send to {recipient}");
                }
            }
            catch (Exception ex)
            {
                result.FailedEmails++;
                result.FailedRecipients.Add(recipient);
                result.ErrorMessages.Add($"Error sending to {recipient}: {ex.Message}");
                _logger.LogError(ex, "Error in bulk email send to: {Recipient}", recipient);
            }
        }

        result.ProcessingTime = DateTime.UtcNow - startTime;

        _logger.LogInformation("Bulk email completed: {Successful}/{Total} successful",
            result.SuccessfulEmails, result.TotalEmails);

        return result;
    }

    public async Task<BulkEmailResult> SendBulkTemplatedEmailAsync(
        List<string> recipients,
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default)
    {
        var result = new BulkEmailResult
        {
            TotalEmails = recipients.Count
        };

        var startTime = DateTime.UtcNow;

        foreach (var recipient in recipients)
        {
            try
            {
                var success = await SendTemplatedEmailAsync(recipient, templateName, templateData, cancellationToken);
                if (success)
                {
                    result.SuccessfulEmails++;
                }
                else
                {
                    result.FailedEmails++;
                    result.FailedRecipients.Add(recipient);
                    result.ErrorMessages.Add($"Failed to send to {recipient}");
                }
            }
            catch (Exception ex)
            {
                result.FailedEmails++;
                result.FailedRecipients.Add(recipient);
                result.ErrorMessages.Add($"Error sending to {recipient}: {ex.Message}");
                _logger.LogError(ex, "Error in bulk templated email send to: {Recipient}", recipient);
            }
        }

        result.ProcessingTime = DateTime.UtcNow - startTime;

        _logger.LogInformation("Bulk templated email completed: {Successful}/{Total} successful",
            result.SuccessfulEmails, result.TotalEmails);

        return result;
    }

    #endregion
