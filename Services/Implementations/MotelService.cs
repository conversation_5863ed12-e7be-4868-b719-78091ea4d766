using Microsoft.Extensions.Logging;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;
using ams_web_mvc.Services.Interfaces;

namespace ams_web_mvc.Services.Implementations;

/// <summary>
/// Service implementation for Motel business logic and operations
/// </summary>
public class MotelService : IMotelService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<MotelService> _logger;

    public MotelService(IUnitOfWork unitOfWork, ILogger<MotelService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region Basic CRUD Operations

    public async Task<Motel?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetByIdAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motel with ID {MotelId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<Motel>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetAllAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all motels");
            throw;
        }
    }

    public async Task<Motel> CreateAsync(Motel motel, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate business rules
            await ValidateMotelAsync(motel, cancellationToken);

            // Set audit fields
            motel.CreatedAt = DateTime.UtcNow;
            motel.UpdatedAt = DateTime.UtcNow;

            // Add to repository
            await _unitOfWork.Motels.AddAsync(motel, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created new motel: {MotelName} (ID: {MotelId})", motel.Name, motel.Id);
            return motel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating motel: {MotelName}", motel.Name);
            throw;
        }
    }

    public async Task<Motel> UpdateAsync(Motel motel, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate business rules
            await ValidateMotelAsync(motel, cancellationToken);

            // Check if motel exists
            var existingMotel = await _unitOfWork.Motels.GetByIdAsync(motel.Id, cancellationToken);
            if (existingMotel == null)
            {
                throw new ArgumentException($"Motel with ID {motel.Id} not found.");
            }

            // Update audit fields
            motel.UpdatedAt = DateTime.UtcNow;
            motel.CreatedAt = existingMotel.CreatedAt; // Preserve original creation date

            // Update in repository
            await _unitOfWork.Motels.UpdateAsync(motel, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated motel: {MotelName} (ID: {MotelId})", motel.Name, motel.Id);
            return motel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating motel: {MotelName} (ID: {MotelId})", motel.Name, motel.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var motel = await _unitOfWork.Motels.GetByIdAsync(id, cancellationToken);
            if (motel == null)
            {
                return false;
            }

            // Check if motel can be deleted (no active alerts, etc.)
            var hasActiveAlerts = await HasActiveAlertsAsync(id, cancellationToken);
            if (hasActiveAlerts)
            {
                throw new InvalidOperationException("Cannot delete motel with active alerts.");
            }

            // Soft delete
            await _unitOfWork.Motels.SoftDeleteAsync(motel, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted motel: {MotelName} (ID: {MotelId})", motel.Name, id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting motel with ID {MotelId}", id);
            throw;
        }
    }

    #endregion

    #region Search and Filtering

    public async Task<IEnumerable<Motel>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return Enumerable.Empty<Motel>();
            }

            return await _unitOfWork.Motels.SearchByNameAsync(searchTerm, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching motels by name: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<Motel>> SearchByLocationAsync(string suburb, string? postcode = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var results = await _unitOfWork.Motels.SearchBySuburbAsync(suburb, cancellationToken);
            
            if (!string.IsNullOrWhiteSpace(postcode))
            {
                var postcodeResults = await _unitOfWork.Motels.SearchByPostcodeAsync(postcode, cancellationToken);
                results = results.Union(postcodeResults);
            }

            return results.Distinct();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching motels by location: {Suburb}, {Postcode}", suburb, postcode);
            throw;
        }
    }

    public async Task<IEnumerable<Motel>> GetByRegionAsync(Region region, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetByRegionAsync(region, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motels by region: {Region}", region);
            throw;
        }
    }

    public async Task<(IEnumerable<Motel> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        Region? region = null,
        List<int>? amenityIds = null,
        List<int>? serviceIds = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.SearchAsync(
                searchTerm, region, null, amenityIds, serviceIds, null,
                pageNumber, pageSize, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing advanced motel search");
            throw;
        }
    }

    #endregion

    #region Amenity and Service Management

    public async Task<Motel> AddAmenityAsync(int motelId, int amenityId, int? amenityOptionId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var motel = await _unitOfWork.Motels.GetByIdAsync(motelId, cancellationToken);
            if (motel == null)
            {
                throw new ArgumentException($"Motel with ID {motelId} not found.");
            }

            // Check if amenity already exists
            var existingAmenity = await _unitOfWork.MotelAmenities.GetByMotelAndAmenityAsync(motelId, amenityId, cancellationToken);
            if (existingAmenity != null)
            {
                throw new InvalidOperationException("Amenity already exists for this motel.");
            }

            // Add amenity
            var motelAmenity = new MotelAmenity
            {
                MotelId = motelId,
                AmenityId = amenityId,
                AmenityOptionId = amenityOptionId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.MotelAmenities.AddAsync(motelAmenity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Added amenity {AmenityId} to motel {MotelId}", amenityId, motelId);
            return await GetWithAmenitiesAsync(motelId, cancellationToken) ?? motel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding amenity {AmenityId} to motel {MotelId}", amenityId, motelId);
            throw;
        }
    }

    public async Task<Motel> RemoveAmenityAsync(int motelId, int amenityId, CancellationToken cancellationToken = default)
    {
        try
        {
            var motel = await _unitOfWork.Motels.GetByIdAsync(motelId, cancellationToken);
            if (motel == null)
            {
                throw new ArgumentException($"Motel with ID {motelId} not found.");
            }

            var motelAmenity = await _unitOfWork.MotelAmenities.GetByMotelAndAmenityAsync(motelId, amenityId, cancellationToken);
            if (motelAmenity != null)
            {
                await _unitOfWork.MotelAmenities.DeleteAsync(motelAmenity, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Removed amenity {AmenityId} from motel {MotelId}", amenityId, motelId);
            }

            return await GetWithAmenitiesAsync(motelId, cancellationToken) ?? motel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing amenity {AmenityId} from motel {MotelId}", amenityId, motelId);
            throw;
        }
    }

    public async Task<Motel> AddServiceAsync(int motelId, int serviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var motel = await _unitOfWork.Motels.GetByIdAsync(motelId, cancellationToken);
            if (motel == null)
            {
                throw new ArgumentException($"Motel with ID {motelId} not found.");
            }

            // Check if service already exists
            var existingService = await _unitOfWork.MotelServices.GetByMotelAndServiceAsync(motelId, serviceId, cancellationToken);
            if (existingService != null)
            {
                throw new InvalidOperationException("Service already exists for this motel.");
            }

            // Add service
            var motelService = new MotelService
            {
                MotelId = motelId,
                ServiceId = serviceId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.MotelServices.AddAsync(motelService, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Added service {ServiceId} to motel {MotelId}", serviceId, motelId);
            return await GetWithServicesAsync(motelId, cancellationToken) ?? motel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding service {ServiceId} to motel {MotelId}", serviceId, motelId);
            throw;
        }
    }

    public async Task<Motel> RemoveServiceAsync(int motelId, int serviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var motel = await _unitOfWork.Motels.GetByIdAsync(motelId, cancellationToken);
            if (motel == null)
            {
                throw new ArgumentException($"Motel with ID {motelId} not found.");
            }

            var motelService = await _unitOfWork.MotelServices.GetByMotelAndServiceAsync(motelId, serviceId, cancellationToken);
            if (motelService != null)
            {
                await _unitOfWork.MotelServices.DeleteAsync(motelService, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Removed service {ServiceId} from motel {MotelId}", serviceId, motelId);
            }

            return await GetWithServicesAsync(motelId, cancellationToken) ?? motel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing service {ServiceId} from motel {MotelId}", serviceId, motelId);
            throw;
        }
    }

    #endregion

    #region Related Data Loading

    public async Task<Motel?> GetWithAmenitiesAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetWithAmenitiesAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motel with amenities: {MotelId}", id);
            throw;
        }
    }

    public async Task<Motel?> GetWithServicesAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetWithServicesAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motel with services: {MotelId}", id);
            throw;
        }
    }

    public async Task<Motel?> GetWithAlertsAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetWithAlertsAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motel with alerts: {MotelId}", id);
            throw;
        }
    }

    public async Task<Motel?> GetWithCommentsAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetWithCommentsAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motel with comments: {MotelId}", id);
            throw;
        }
    }

    public async Task<Motel?> GetWithAllDataAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetWithAllRelatedDataAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving motel with all data: {MotelId}", id);
            throw;
        }
    }

    #endregion

    #region Business Logic

    public async Task<bool> HasActiveAlertsAsync(int motelId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.HasActiveAlertsAsync(motelId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking active alerts for motel: {MotelId}", motelId);
            throw;
        }
    }

    public async Task<int> GetActiveAlertCountAsync(int motelId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetActiveAlertCountAsync(motelId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alert count for motel: {MotelId}", motelId);
            throw;
        }
    }

    public async Task<int> GetPendingCommentCountAsync(int motelId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetPendingCommentCountAsync(motelId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending comment count for motel: {MotelId}", motelId);
            throw;
        }
    }

    #endregion

    #region Statistics

    public async Task<Dictionary<Region, int>> GetCountsByRegionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.GetCountsByRegionAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting motel counts by region");
            throw;
        }
    }

    public async Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total motel count");
            throw;
        }
    }

    #endregion

    #region Validation

    public async Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Motels.ExistsAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if motel exists: {MotelId}", id);
            throw;
        }
    }

    public async Task<bool> IsNameUniqueAsync(string name, int? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingMotel = await _unitOfWork.Motels.FindAsync(m => m.Name == name, cancellationToken);
            var existing = existingMotel.FirstOrDefault();

            if (existing == null)
                return true;

            return excludeId.HasValue && existing.Id == excludeId.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking name uniqueness: {MotelName}", name);
            throw;
        }
    }

    #endregion

    #region Private Helper Methods

    private async Task ValidateMotelAsync(Motel motel, CancellationToken cancellationToken)
    {
        // Validate required fields
        if (string.IsNullOrWhiteSpace(motel.Name))
        {
            throw new ArgumentException("Motel name is required.");
        }

        if (string.IsNullOrWhiteSpace(motel.Address))
        {
            throw new ArgumentException("Motel address is required.");
        }

        if (string.IsNullOrWhiteSpace(motel.Suburb))
        {
            throw new ArgumentException("Motel suburb is required.");
        }

        if (string.IsNullOrWhiteSpace(motel.Postcode))
        {
            throw new ArgumentException("Motel postcode is required.");
        }

        // Validate name uniqueness
        var isNameUnique = await IsNameUniqueAsync(motel.Name, motel.Id == 0 ? null : motel.Id, cancellationToken);
        if (!isNameUnique)
        {
            throw new ArgumentException($"A motel with the name '{motel.Name}' already exists.");
        }

        // Additional business rule validations can be added here
    }

    #endregion
}
