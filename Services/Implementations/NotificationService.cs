using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;
using ams_web_mvc.Repositories.Interfaces;
using ams_web_mvc.Services.Interfaces;

namespace ams_web_mvc.Services.Implementations;

/// <summary>
/// Service implementation for notification management and delivery
/// </summary>
public class NotificationService : INotificationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IEmailService _emailService;
    private readonly ILogger<NotificationService> _logger;
    private readonly IMemoryCache _cache;
    private const int CACHE_DURATION_MINUTES = 10;

    public NotificationService(
        IUnitOfWork unitOfWork,
        IEmailService emailService,
        ILogger<NotificationService> logger,
        IMemoryCache cache)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
    }

    #region Notification Creation

    public async Task<Notification> CreateNotificationAsync(
        string title,
        string message,
        NotificationType type,
        string? recipientId = null,
        int? relatedEntityId = null,
        string? relatedEntityType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new Notification
            {
                Title = title,
                Message = message,
                Type = type,
                RecipientId = recipientId,
                RelatedEntityId = relatedEntityId,
                RelatedEntityType = relatedEntityType,
                Status = NotificationStatus.Draft,
                CreatedAt = DateTime.UtcNow
            };

            var createdNotification = await _unitOfWork.Notifications.AddAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created notification: {NotificationId} - {Title}", 
                createdNotification.Id, createdNotification.Title);

            // Clear cache
            _cache.Remove($"user_notifications_{recipientId}");
            _cache.Remove($"unread_count_{recipientId}");

            return createdNotification;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating notification: {Title}", title);
            throw;
        }
    }

    public async Task<Notification> CreateAlertNotificationAsync(
        Alert alert,
        NotificationType type,
        string? customMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var title = type switch
            {
                NotificationType.AlertCreated => $"New Alert: {alert.Title}",
                NotificationType.AlertUpdated => $"Alert Updated: {alert.Title}",
                NotificationType.AlertClosed => $"Alert Closed: {alert.Title}",
                NotificationType.AlertExpired => $"Alert Expired: {alert.Title}",
                _ => $"Alert Notification: {alert.Title}"
            };

            var message = customMessage ?? GenerateAlertMessage(alert, type);

            return await CreateNotificationAsync(
                title: title,
                message: message,
                type: type,
                relatedEntityId: alert.Id,
                relatedEntityType: nameof(Alert),
                cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert notification for alert: {AlertId}", alert.Id);
            throw;
        }
    }

    public async Task<Notification> CreateCommentNotificationAsync(
        Comment comment,
        NotificationType type,
        string? customMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var title = type switch
            {
                NotificationType.CommentSubmitted => "New Comment Submitted",
                NotificationType.CommentApproved => "Comment Approved",
                NotificationType.CommentRejected => "Comment Rejected",
                _ => "Comment Notification"
            };

            var message = customMessage ?? GenerateCommentMessage(comment, type);

            return await CreateNotificationAsync(
                title: title,
                message: message,
                type: type,
                recipientId: comment.AuthorId,
                relatedEntityId: comment.Id,
                relatedEntityType: nameof(Comment),
                cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating comment notification for comment: {CommentId}", comment.Id);
            throw;
        }
    }

    #endregion

    #region Notification Retrieval

    public async Task<Notification?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Notifications.GetByIdAsync(id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification by ID: {NotificationId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<Notification>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"user_notifications_{userId}";
            
            if (_cache.TryGetValue(cacheKey, out IEnumerable<Notification>? cachedNotifications))
            {
                return cachedNotifications ?? Enumerable.Empty<Notification>();
            }

            var notifications = await _unitOfWork.Notifications.GetByUserIdAsync(userId, cancellationToken);
            
            _cache.Set(cacheKey, notifications, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            
            return notifications;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notifications for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<Notification>> GetUnreadByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var allNotifications = await GetByUserIdAsync(userId, cancellationToken);
            return allNotifications.Where(n => !n.IsRead);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unread notifications for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<Notification>> GetPendingApprovalAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            const string cacheKey = "pending_approval_notifications";
            
            if (_cache.TryGetValue(cacheKey, out IEnumerable<Notification>? cachedNotifications))
            {
                return cachedNotifications ?? Enumerable.Empty<Notification>();
            }

            var notifications = await _unitOfWork.Notifications.GetPendingApprovalAsync(cancellationToken);
            
            _cache.Set(cacheKey, notifications, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            
            return notifications;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending approval notifications");
            throw;
        }
    }

    #endregion

    #region Notification Management

    public async Task<bool> MarkAsReadAsync(int notificationId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null || notification.RecipientId != userId)
            {
                return false;
            }

            notification.IsRead = true;
            notification.ReadAt = DateTime.UtcNow;

            await _unitOfWork.Notifications.UpdateAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove($"user_notifications_{userId}");
            _cache.Remove($"unread_count_{userId}");

            _logger.LogInformation("Marked notification as read: {NotificationId} for user: {UserId}", 
                notificationId, userId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notification as read: {NotificationId}", notificationId);
            return false;
        }
    }

    public async Task<bool> MarkAllAsReadAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var unreadNotifications = await GetUnreadByUserIdAsync(userId, cancellationToken);
            
            foreach (var notification in unreadNotifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
                await _unitOfWork.Notifications.UpdateAsync(notification, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove($"user_notifications_{userId}");
            _cache.Remove($"unread_count_{userId}");

            _logger.LogInformation("Marked all notifications as read for user: {UserId}", userId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking all notifications as read for user: {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> DeleteNotificationAsync(int notificationId, string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null || notification.RecipientId != userId)
            {
                return false;
            }

            await _unitOfWork.Notifications.DeleteAsync(notificationId, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove($"user_notifications_{userId}");
            _cache.Remove($"unread_count_{userId}");

            _logger.LogInformation("Deleted notification: {NotificationId} for user: {UserId}", 
                notificationId, userId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting notification: {NotificationId}", notificationId);
            return false;
        }
    }

    #endregion

    #region Approval Workflow

    public async Task<bool> SubmitForApprovalAsync(int notificationId, string submitterId, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null || notification.Status != NotificationStatus.Draft)
            {
                return false;
            }

            notification.Status = NotificationStatus.PendingApproval;
            notification.SubmittedAt = DateTime.UtcNow;
            notification.SubmitterId = submitterId;

            await _unitOfWork.Notifications.UpdateAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove("pending_approval_notifications");

            _logger.LogInformation("Notification submitted for approval: {NotificationId} by {SubmitterId}",
                notificationId, submitterId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting notification for approval: {NotificationId}", notificationId);
            return false;
        }
    }

    public async Task<bool> ApproveNotificationAsync(int notificationId, string approverId, string? approvalNotes = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null || notification.Status != NotificationStatus.PendingApproval)
            {
                return false;
            }

            notification.Status = NotificationStatus.Approved;
            notification.ApprovedAt = DateTime.UtcNow;
            notification.ApproverId = approverId;
            notification.ApprovalNotes = approvalNotes;

            await _unitOfWork.Notifications.UpdateAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove("pending_approval_notifications");

            // Send the notification immediately after approval
            await SendNotificationAsync(notificationId, cancellationToken);

            _logger.LogInformation("Notification approved: {NotificationId} by {ApproverId}",
                notificationId, approverId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving notification: {NotificationId}", notificationId);
            return false;
        }
    }

    public async Task<bool> RejectNotificationAsync(int notificationId, string approverId, string rejectionReason, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null || notification.Status != NotificationStatus.PendingApproval)
            {
                return false;
            }

            notification.Status = NotificationStatus.Rejected;
            notification.RejectedAt = DateTime.UtcNow;
            notification.ApproverId = approverId;
            notification.RejectionReason = rejectionReason;

            await _unitOfWork.Notifications.UpdateAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove("pending_approval_notifications");

            _logger.LogInformation("Notification rejected: {NotificationId} by {ApproverId}",
                notificationId, approverId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting notification: {NotificationId}", notificationId);
            return false;
        }
    }

    #endregion

    #region Notification Delivery

    public async Task<bool> SendNotificationAsync(int notificationId, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null || notification.Status != NotificationStatus.Approved)
            {
                return false;
            }

            var success = await _emailService.SendNotificationEmailAsync(notification, cancellationToken);

            notification.Status = success ? NotificationStatus.Sent : NotificationStatus.Failed;
            notification.SentAt = success ? DateTime.UtcNow : null;
            notification.DeliveryAttempts = (notification.DeliveryAttempts ?? 0) + 1;

            await _unitOfWork.Notifications.UpdateAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification delivery {Status}: {NotificationId}",
                success ? "succeeded" : "failed", notificationId);

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification: {NotificationId}", notificationId);
            return false;
        }
    }

    public async Task<bool> SendBulkNotificationsAsync(List<int> notificationIds, CancellationToken cancellationToken = default)
    {
        try
        {
            var successCount = 0;

            foreach (var notificationId in notificationIds)
            {
                var success = await SendNotificationAsync(notificationId, cancellationToken);
                if (success) successCount++;
            }

            _logger.LogInformation("Bulk notification delivery completed: {SuccessCount}/{TotalCount}",
                successCount, notificationIds.Count);

            return successCount == notificationIds.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk notifications");
            return false;
        }
    }

    public async Task<bool> SendImmediateNotificationAsync(Notification notification, CancellationToken cancellationToken = default)
    {
        try
        {
            // Create and immediately send notification
            var createdNotification = await _unitOfWork.Notifications.AddAsync(notification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            createdNotification.Status = NotificationStatus.Approved;
            await _unitOfWork.Notifications.UpdateAsync(createdNotification, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return await SendNotificationAsync(createdNotification.Id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending immediate notification");
            return false;
        }
    }

    #endregion

    #region Private Helper Methods

    private string GenerateAlertMessage(Alert alert, NotificationType type)
    {
        return type switch
        {
            NotificationType.AlertCreated => $"A new {alert.Type} alert has been created for {alert.Motel?.Name}: {alert.Description}",
            NotificationType.AlertUpdated => $"The {alert.Type} alert for {alert.Motel?.Name} has been updated: {alert.Description}",
            NotificationType.AlertClosed => $"The {alert.Type} alert for {alert.Motel?.Name} has been closed.",
            NotificationType.AlertExpired => $"The {alert.Type} alert for {alert.Motel?.Name} has expired.",
            _ => $"Alert notification for {alert.Motel?.Name}: {alert.Description}"
        };
    }

    private string GenerateCommentMessage(Comment comment, NotificationType type)
    {
        return type switch
        {
            NotificationType.CommentSubmitted => $"Your comment on {comment.Motel?.Name} has been submitted for moderation.",
            NotificationType.CommentApproved => $"Your comment on {comment.Motel?.Name} has been approved and is now visible.",
            NotificationType.CommentRejected => $"Your comment on {comment.Motel?.Name} has been rejected. Please review and resubmit if necessary.",
            _ => $"Comment notification for {comment.Motel?.Name}"
        };
    }

    #endregion
