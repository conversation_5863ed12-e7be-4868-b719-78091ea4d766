using ams_web_mvc.Models;
using ams_web_mvc.Models.DTOs;

namespace ams_web_mvc.Services;

public interface IAuthenticationService
{
    Task<AuthResult> LoginAsync(LoginDto loginDto);
    Task<AuthResult> LoginWithJwtAsync(LoginDto loginDto);
    Task LogoutAsync();
    Task<AuthResult> RegisterAsync(RegisterDto registerDto);
    Task<AuthResult> InviteUserAsync(InviteUserDto inviteDto, ApplicationUser invitingUser);
    Task<AuthResult> AcceptInvitationAsync(AcceptInvitationDto acceptDto);
    Task<string> GenerateJwtTokenAsync(ApplicationUser user);
    Task<bool> ValidatePasswordAsync(ApplicationUser user, string password);
    Task<bool> IsPasswordExpiredAsync(ApplicationUser user);
    Task<AuthResult> ChangePasswordAsync(string userId, ChangePasswordDto changePasswordDto);
    Task<AuthResult> ResetPasswordAsync(ResetPasswordDto resetPasswordDto);
}

public class AuthResult
{
    public bool Success { get; set; }
    public string? Token { get; set; }
    public ApplicationUser? User { get; set; }
    public List<string> Errors { get; set; } = new();
    public string? Message { get; set; }
}


