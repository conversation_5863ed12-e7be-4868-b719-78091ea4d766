using ams_web_mvc.Models;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for email delivery and template management
/// </summary>
public interface IEmailService
{
    // Basic email sending
    Task<bool> SendEmailAsync(
        string to,
        string subject,
        string body,
        bool isHtml = true,
        CancellationToken cancellationToken = default);

    Task<bool> SendEmailAsync(
        List<string> to,
        string subject,
        string body,
        bool isHtml = true,
        List<string>? cc = null,
        List<string>? bcc = null,
        CancellationToken cancellationToken = default);

    // Template-based email sending
    Task<bool> SendTemplatedEmailAsync(
        string to,
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default);

    Task<bool> SendTemplatedEmailAsync(
        List<string> to,
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default);

    // Notification-specific emails
    Task<bool> SendNotificationEmailAsync(
        Notification notification,
        CancellationToken cancellationToken = default);

    Task<bool> SendAlertNotificationAsync(
        Alert alert,
        ApplicationUser recipient,
        string? customMessage = null,
        CancellationToken cancellationToken = default);

    Task<bool> SendCommentNotificationAsync(
        Comment comment,
        ApplicationUser recipient,
        string? customMessage = null,
        CancellationToken cancellationToken = default);

    // System emails
    Task<bool> SendWelcomeEmailAsync(
        ApplicationUser user,
        string? invitationMessage = null,
        CancellationToken cancellationToken = default);

    Task<bool> SendPasswordResetEmailAsync(
        ApplicationUser user,
        string resetToken,
        CancellationToken cancellationToken = default);

    Task<bool> SendAccountLockedEmailAsync(
        ApplicationUser user,
        CancellationToken cancellationToken = default);

    // Digest emails
    Task<bool> SendDailyDigestAsync(
        ApplicationUser user,
        DigestData digestData,
        CancellationToken cancellationToken = default);

    Task<bool> SendWeeklyDigestAsync(
        ApplicationUser user,
        DigestData digestData,
        CancellationToken cancellationToken = default);

    Task<bool> SendMonthlyDigestAsync(
        ApplicationUser user,
        DigestData digestData,
        CancellationToken cancellationToken = default);

    // Bulk operations
    Task<BulkEmailResult> SendBulkEmailAsync(
        List<string> recipients,
        string subject,
        string body,
        bool isHtml = true,
        CancellationToken cancellationToken = default);

    Task<BulkEmailResult> SendBulkTemplatedEmailAsync(
        List<string> recipients,
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default);

    // Template management
    Task<string> RenderTemplateAsync(
        string templateName,
        object templateData,
        CancellationToken cancellationToken = default);

    Task<bool> ValidateTemplateAsync(
        string templateName,
        CancellationToken cancellationToken = default);

    // Email validation and utilities
    Task<bool> ValidateEmailAddressAsync(string email);
    Task<bool> IsEmailDeliverable(string email);
    Task<EmailDeliveryStatus> GetDeliveryStatusAsync(string messageId);
}

/// <summary>
/// Data for digest emails
/// </summary>
public class DigestData
{
    public List<Alert> NewAlerts { get; set; } = new();
    public List<Comment> NewComments { get; set; } = new();
    public List<Notification> UnreadNotifications { get; set; } = new();
    public Dictionary<string, int> ActivitySummary { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

/// <summary>
/// Result of bulk email operations
/// </summary>
public class BulkEmailResult
{
    public int TotalEmails { get; set; }
    public int SuccessfulEmails { get; set; }
    public int FailedEmails { get; set; }
    public List<string> FailedRecipients { get; set; } = new();
    public List<string> ErrorMessages { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// Email delivery status
/// </summary>
public enum EmailDeliveryStatus
{
    Unknown = 0,
    Pending = 1,
    Sent = 2,
    Delivered = 3,
    Bounced = 4,
    Failed = 5,
    Rejected = 6
}
