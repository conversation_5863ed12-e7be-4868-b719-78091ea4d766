using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for advanced search and filtering capabilities
/// </summary>
public interface ISearchService
{
    // Full-text search
    Task<(IEnumerable<Motel> Items, int TotalCount)> FullTextSearchAsync(
        string searchTerm,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Advanced motel search with multiple criteria
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchMotelsAsync(
        MotelSearchCriteria criteria,
        CancellationToken cancellationToken = default);

    // Quick search suggestions
    Task<IEnumerable<string>> GetSearchSuggestionsAsync(
        string searchTerm,
        int maxResults = 10,
        CancellationToken cancellationToken = default);

    // Search by location
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchByLocationAsync(
        string? suburb = null,
        string? postcode = null,
        Region? region = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Search with amenity filtering
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchWithAmenitiesAsync(
        List<int> amenityIds,
        bool requireAll = false,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Search with service filtering
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchWithServicesAsync(
        List<int> serviceIds,
        bool requireAll = false,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Exclusion search (exclude specific motels)
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchWithExclusionsAsync(
        List<int> excludeMotelIds,
        string? searchTerm = null,
        Region? region = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Tab-based searches
    Task<(IEnumerable<Motel> Items, int TotalCount)> GetMotelsWithAlertsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    Task<(IEnumerable<Motel> Items, int TotalCount)> GetMotelsWithSuggestionsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    Task<(IEnumerable<Motel> Items, int TotalCount)> GetMotelsWithCommentsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Search statistics
    Task<SearchStatistics> GetSearchStatisticsAsync(CancellationToken cancellationToken = default);

    // Popular searches
    Task<IEnumerable<string>> GetPopularSearchTermsAsync(
        int maxResults = 10,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Search criteria for advanced motel search
/// </summary>
public class MotelSearchCriteria
{
    public string? SearchTerm { get; set; }
    public Region? Region { get; set; }
    public string? Suburb { get; set; }
    public string? Postcode { get; set; }
    public List<int>? AmenityIds { get; set; }
    public List<int>? ServiceIds { get; set; }
    public List<int>? ExcludeMotelIds { get; set; }
    public bool RequireAllAmenities { get; set; } = false;
    public bool RequireAllServices { get; set; } = false;
    public bool HasActiveAlerts { get; set; } = false;
    public bool HasPendingComments { get; set; } = false;
    public bool HasSuggestions { get; set; } = false;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "Name";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Search statistics for reporting
/// </summary>
public class SearchStatistics
{
    public int TotalMotels { get; set; }
    public int MotelsWithAlerts { get; set; }
    public int MotelsWithComments { get; set; }
    public int MotelsWithSuggestions { get; set; }
    public Dictionary<Region, int> MotelsByRegion { get; set; } = new();
    public Dictionary<string, int> PopularSearchTerms { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}
