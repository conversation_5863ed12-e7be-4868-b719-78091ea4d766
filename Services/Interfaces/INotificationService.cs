using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for notification management and delivery
/// </summary>
public interface INotificationService
{
    // Notification creation
    Task<Notification> CreateNotificationAsync(
        string title,
        string message,
        NotificationType type,
        string? recipientId = null,
        int? relatedEntityId = null,
        string? relatedEntityType = null,
        CancellationToken cancellationToken = default);

    Task<Notification> CreateAlertNotificationAsync(
        Alert alert,
        NotificationType type,
        string? customMessage = null,
        CancellationToken cancellationToken = default);

    Task<Notification> CreateCommentNotificationAsync(
        Comment comment,
        NotificationType type,
        string? customMessage = null,
        CancellationToken cancellationToken = default);

    // Notification retrieval
    Task<Notification?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Notification>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Notification>> GetUnreadByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Notification>> GetPendingApprovalAsync(CancellationToken cancellationToken = default);

    // Notification management
    Task<bool> MarkAsReadAsync(int notificationId, string userId, CancellationToken cancellationToken = default);
    Task<bool> MarkAllAsReadAsync(string userId, CancellationToken cancellationToken = default);
    Task<bool> DeleteNotificationAsync(int notificationId, string userId, CancellationToken cancellationToken = default);

    // Approval workflow
    Task<bool> SubmitForApprovalAsync(int notificationId, string submitterId, CancellationToken cancellationToken = default);
    Task<bool> ApproveNotificationAsync(int notificationId, string approverId, string? approvalNotes = null, CancellationToken cancellationToken = default);
    Task<bool> RejectNotificationAsync(int notificationId, string approverId, string rejectionReason, CancellationToken cancellationToken = default);

    // Notification delivery
    Task<bool> SendNotificationAsync(int notificationId, CancellationToken cancellationToken = default);
    Task<bool> SendBulkNotificationsAsync(List<int> notificationIds, CancellationToken cancellationToken = default);
    Task<bool> SendImmediateNotificationAsync(Notification notification, CancellationToken cancellationToken = default);

    // Notification preferences
    Task<NotificationPreferences> GetUserPreferencesAsync(string userId, CancellationToken cancellationToken = default);
    Task<bool> UpdateUserPreferencesAsync(string userId, NotificationPreferences preferences, CancellationToken cancellationToken = default);

    // Statistics and reporting
    Task<NotificationStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Notification>> GetRecentNotificationsAsync(int count = 10, CancellationToken cancellationToken = default);
    Task<int> GetUnreadCountAsync(string userId, CancellationToken cancellationToken = default);

    // Cleanup and maintenance
    Task<int> CleanupOldNotificationsAsync(int daysOld = 90, CancellationToken cancellationToken = default);
    Task<int> CleanupReadNotificationsAsync(int daysOld = 30, CancellationToken cancellationToken = default);
}

/// <summary>
/// User notification preferences
/// </summary>
public class NotificationPreferences
{
    public string UserId { get; set; } = string.Empty;
    public bool EmailNotifications { get; set; } = true;
    public bool AlertNotifications { get; set; } = true;
    public bool CommentNotifications { get; set; } = true;
    public bool SystemNotifications { get; set; } = true;
    public bool DigestEmails { get; set; } = true;
    public DigestFrequency DigestFrequency { get; set; } = DigestFrequency.Daily;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Notification statistics for reporting
/// </summary>
public class NotificationStatistics
{
    public int TotalNotifications { get; set; }
    public int PendingApproval { get; set; }
    public int Approved { get; set; }
    public int Rejected { get; set; }
    public int Sent { get; set; }
    public int Failed { get; set; }
    public Dictionary<NotificationType, int> NotificationsByType { get; set; } = new();
    public Dictionary<string, int> NotificationsByUser { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Digest frequency options
/// </summary>
public enum DigestFrequency
{
    Never = 0,
    Daily = 1,
    Weekly = 2,
    Monthly = 3
}
