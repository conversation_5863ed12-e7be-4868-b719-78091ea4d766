using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for Alert business logic and workflow management
/// </summary>
public interface IAlertService
{
    // Basic CRUD operations
    Task<Alert?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Alert> CreateAsync(Alert alert, string userId, CancellationToken cancellationToken = default);
    Task<Alert> UpdateAsync(Alert alert, string userId, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(int id, string userId, CancellationToken cancellationToken = default);
    
    // Alert lifecycle management
    Task<Alert> CloseAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default);
    Task<Alert> ReopenAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default);
    Task<Alert> DiscardAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default);
    Task<Alert> RestoreAlertAsync(int alertId, string userId, CancellationToken cancellationToken = default);
    
    // Filtering and search
    Task<IEnumerable<Alert>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByUserIdAsync(string userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByAlertTypeAsync(AlertType alertType, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetClosedAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetArchivedAlertsAsync(CancellationToken cancellationToken = default);
    
    // Date-based queries
    Task<IEnumerable<Alert>> GetRecentAlertsAsync(int days = 30, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetExpiredAlertsAsync(CancellationToken cancellationToken = default);
    
    // Advanced search with pagination
    Task<(IEnumerable<Alert> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        AlertType? alertType = null,
        int? motelId = null,
        string? userId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        bool? isActive = null,
        bool? isClosed = null,
        bool? isArchived = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Alert?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default);
    Task<Alert?> GetWithUserAsync(int id, CancellationToken cancellationToken = default);
    Task<Alert?> GetWithAllDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Business logic
    Task<bool> CanUserEditAsync(int alertId, string userId, CancellationToken cancellationToken = default);
    Task<bool> CanUserDeleteAsync(int alertId, string userId, CancellationToken cancellationToken = default);
    Task<bool> IsExpiredAsync(int alertId, CancellationToken cancellationToken = default);
    
    // Statistics
    Task<Dictionary<AlertType, int>> GetCountsByTypeAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetClosedCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetArchivedCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetCountByMotelAsync(int motelId, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> BulkCloseAsync(List<int> alertIds, string userId, CancellationToken cancellationToken = default);
    Task<int> BulkArchiveExpiredAsync(CancellationToken cancellationToken = default);
    
    // Validation
    Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default);
}
