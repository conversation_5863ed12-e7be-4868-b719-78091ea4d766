using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for Comment business logic and moderation workflow
/// </summary>
public interface ICommentService
{
    // Basic CRUD operations
    Task<Comment?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Comment> CreateAsync(Comment comment, string userId, CancellationToken cancellationToken = default);
    Task<Comment> UpdateAsync(Comment comment, string userId, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(int id, string userId, CancellationToken cancellationToken = default);
    
    // Moderation workflow
    Task<Comment> ApproveAsync(int commentId, string moderatorId, string? moderationNotes = null, CancellationToken cancellationToken = default);
    Task<Comment> RejectAsync(int commentId, string moderatorId, string rejectionReason, CancellationToken cancellationToken = default);
    Task<Comment> RequestReviewAsync(int commentId, string userId, CancellationToken cancellationToken = default);
    
    // Status-based queries
    Task<IEnumerable<Comment>> GetPendingAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetApprovedAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetRejectedAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetByStatusAsync(CommentStatus status, CancellationToken cancellationToken = default);
    
    // Filtering and search
    Task<IEnumerable<Comment>> GetByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetApprovedByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetPendingByMotelIdAsync(int motelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetByAuthorIdAsync(string authorId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetByModeratorIdAsync(string moderatorId, CancellationToken cancellationToken = default);
    
    // Date-based queries
    Task<IEnumerable<Comment>> GetRecentAsync(int days = 30, CancellationToken cancellationToken = default);
    Task<IEnumerable<Comment>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    // Advanced search with pagination
    Task<(IEnumerable<Comment> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        CommentStatus? status = null,
        int? motelId = null,
        string? authorId = null,
        string? moderatorId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Comment?> GetWithMotelAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithAuthorAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithModeratorAsync(int id, CancellationToken cancellationToken = default);
    Task<Comment?> GetWithAllDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Business logic
    Task<bool> CanUserEditAsync(int commentId, string userId, CancellationToken cancellationToken = default);
    Task<bool> CanUserDeleteAsync(int commentId, string userId, CancellationToken cancellationToken = default);
    Task<bool> CanUserModerateAsync(string userId, CancellationToken cancellationToken = default);
    Task<bool> RequiresModerationAsync(Comment comment, CancellationToken cancellationToken = default);
    
    // Statistics
    Task<Dictionary<CommentStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default);
    Task<int> GetPendingCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetApprovedCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetRejectedCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetCountByMotelAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetPendingCountByMotelAsync(int motelId, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<int> BulkApproveAsync(List<int> commentIds, string moderatorId, CancellationToken cancellationToken = default);
    Task<int> BulkRejectAsync(List<int> commentIds, string moderatorId, string rejectionReason, CancellationToken cancellationToken = default);
    
    // Validation
    Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> IsContentAppropriateAsync(string content, CancellationToken cancellationToken = default);
}
