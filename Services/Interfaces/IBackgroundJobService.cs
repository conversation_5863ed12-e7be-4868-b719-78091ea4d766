using ams_web_mvc.Models;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for background job processing
/// </summary>
public interface IBackgroundJobService
{
    // Notification processing jobs
    Task ProcessPendingNotificationsAsync(CancellationToken cancellationToken = default);
    Task ProcessFailedNotificationsAsync(CancellationToken cancellationToken = default);
    Task SendDigestEmailsAsync(DigestFrequency frequency, CancellationToken cancellationToken = default);

    // Alert processing jobs
    Task ProcessExpiredAlertsAsync(CancellationToken cancellationToken = default);
    Task ArchiveOldAlertsAsync(int daysOld = 90, CancellationToken cancellationToken = default);
    Task SendAlertReminderEmailsAsync(CancellationToken cancellationToken = default);

    // Comment processing jobs
    Task ProcessPendingCommentsAsync(CancellationToken cancellationToken = default);
    Task CleanupOldCommentsAsync(int daysOld = 365, CancellationToken cancellationToken = default);

    // System maintenance jobs
    Task CleanupOldNotificationsAsync(int daysOld = 90, CancellationToken cancellationToken = default);
    Task CleanupReadNotificationsAsync(int daysOld = 30, CancellationToken cancellationToken = default);
    Task GenerateSystemReportsAsync(CancellationToken cancellationToken = default);

    // Job scheduling
    Task ScheduleRecurringJobsAsync();
    Task<string> ScheduleDelayedJobAsync(string jobName, TimeSpan delay, object? parameters = null);
    Task<bool> CancelJobAsync(string jobId);

    // Job monitoring
    Task<JobStatus> GetJobStatusAsync(string jobId);
    Task<IEnumerable<JobInfo>> GetRunningJobsAsync();
    Task<IEnumerable<JobInfo>> GetFailedJobsAsync();
    Task<JobStatistics> GetJobStatisticsAsync();
}

/// <summary>
/// Job status enumeration
/// </summary>
public enum JobStatus
{
    Unknown = 0,
    Scheduled = 1,
    Running = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

/// <summary>
/// Job information
/// </summary>
public class JobInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public JobStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string? ErrorMessage { get; set; }
    public int RetryCount { get; set; }
    public object? Parameters { get; set; }
}

/// <summary>
/// Job statistics
/// </summary>
public class JobStatistics
{
    public int TotalJobs { get; set; }
    public int CompletedJobs { get; set; }
    public int FailedJobs { get; set; }
    public int RunningJobs { get; set; }
    public int ScheduledJobs { get; set; }
    public Dictionary<string, int> JobsByType { get; set; } = new();
    public TimeSpan AverageExecutionTime { get; set; }
    public DateTime LastUpdated { get; set; }
}
