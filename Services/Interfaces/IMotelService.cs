using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for Motel business logic and operations
/// </summary>
public interface IMotelService
{
    // Basic CRUD operations
    Task<Motel?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Motel> CreateAsync(Motel motel, CancellationToken cancellationToken = default);
    Task<Motel> UpdateAsync(Motel motel, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);
    
    // Search and filtering
    Task<IEnumerable<Motel>> SearchByNameAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> SearchByLocationAsync(string suburb, string? postcode = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Motel>> GetByRegionAsync(Region region, CancellationToken cancellationToken = default);
    
    // Advanced search with pagination
    Task<(IEnumerable<Motel> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        Region? region = null,
        List<int>? amenityIds = null,
        List<int>? serviceIds = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    // Amenity and service management
    Task<Motel> AddAmenityAsync(int motelId, int amenityId, int? amenityOptionId = null, CancellationToken cancellationToken = default);
    Task<Motel> RemoveAmenityAsync(int motelId, int amenityId, CancellationToken cancellationToken = default);
    Task<Motel> AddServiceAsync(int motelId, int serviceId, CancellationToken cancellationToken = default);
    Task<Motel> RemoveServiceAsync(int motelId, int serviceId, CancellationToken cancellationToken = default);
    
    // Related data loading
    Task<Motel?> GetWithAmenitiesAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithServicesAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithAlertsAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithCommentsAsync(int id, CancellationToken cancellationToken = default);
    Task<Motel?> GetWithAllDataAsync(int id, CancellationToken cancellationToken = default);
    
    // Business logic
    Task<bool> HasActiveAlertsAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetActiveAlertCountAsync(int motelId, CancellationToken cancellationToken = default);
    Task<int> GetPendingCommentCountAsync(int motelId, CancellationToken cancellationToken = default);
    
    // Statistics
    Task<Dictionary<Region, int>> GetCountsByRegionAsync(CancellationToken cancellationToken = default);
    Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default);
    
    // Validation
    Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> IsNameUniqueAsync(string name, int? excludeId = null, CancellationToken cancellationToken = default);
}
