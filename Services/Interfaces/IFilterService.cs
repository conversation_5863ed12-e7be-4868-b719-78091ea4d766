using ams_web_mvc.Models;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Services.Interfaces;

/// <summary>
/// Service interface for advanced filtering and faceted search
/// </summary>
public interface IFilterService
{
    // Filter options
    Task<FilterOptions> GetFilterOptionsAsync(CancellationToken cancellationToken = default);
    
    // Apply filters
    Task<(IEnumerable<Motel> Items, int TotalCount)> ApplyFiltersAsync(
        FilterCriteria criteria,
        CancellationToken cancellationToken = default);

    // Get filter counts (faceted search)
    Task<FilterCounts> GetFilterCountsAsync(
        FilterCriteria baseCriteria,
        CancellationToken cancellationToken = default);

    // Quick filters
    Task<(IEnumerable<Motel> Items, int TotalCount)> GetRecentlyUpdatedAsync(
        int days = 7,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    Task<(IEnumerable<Motel> Items, int TotalCount)> GetRecentlyAddedAsync(
        int days = 30,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Saved filters
    Task<SavedFilter> SaveFilterAsync(SavedFilter filter, CancellationToken cancellationToken = default);
    Task<IEnumerable<SavedFilter>> GetSavedFiltersAsync(string userId, CancellationToken cancellationToken = default);
    Task<bool> DeleteSavedFilterAsync(int filterId, string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Filter criteria for advanced filtering
/// </summary>
public class FilterCriteria
{
    public string? SearchTerm { get; set; }
    public List<Region>? Regions { get; set; }
    public List<string>? Suburbs { get; set; }
    public List<string>? Postcodes { get; set; }
    public List<int>? AmenityIds { get; set; }
    public List<int>? ServiceIds { get; set; }
    public List<int>? ExcludeMotelIds { get; set; }
    public bool? HasActiveAlerts { get; set; }
    public bool? HasPendingComments { get; set; }
    public bool? HasSuggestions { get; set; }
    public bool? HasCautions { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? UpdatedAfter { get; set; }
    public DateTime? UpdatedBefore { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string SortBy { get; set; } = "Name";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Available filter options
/// </summary>
public class FilterOptions
{
    public List<Region> AvailableRegions { get; set; } = new();
    public List<string> AvailableSuburbs { get; set; } = new();
    public List<string> AvailablePostcodes { get; set; } = new();
    public List<Amenity> AvailableAmenities { get; set; } = new();
    public List<Service> AvailableServices { get; set; } = new();
    public List<string> SortOptions { get; set; } = new();
}

/// <summary>
/// Filter counts for faceted search
/// </summary>
public class FilterCounts
{
    public Dictionary<Region, int> RegionCounts { get; set; } = new();
    public Dictionary<string, int> SuburbCounts { get; set; } = new();
    public Dictionary<int, int> AmenityCounts { get; set; } = new();
    public Dictionary<int, int> ServiceCounts { get; set; } = new();
    public int WithAlertsCount { get; set; }
    public int WithCommentsCount { get; set; }
    public int WithSuggestionsCount { get; set; }
    public int WithCautionsCount { get; set; }
    public int TotalCount { get; set; }
}

/// <summary>
/// Saved filter for users
/// </summary>
public class SavedFilter
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public FilterCriteria Criteria { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsPublic { get; set; } = false;
}
