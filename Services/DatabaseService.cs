using Microsoft.EntityFrameworkCore;
using ams_web_mvc.Data;

namespace ams_web_mvc.Services;

public class DatabaseService : IDatabaseService
{
    private readonly ApplicationDbContext _context;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseService> _logger;
    private readonly IWebHostEnvironment _environment;

    public DatabaseService(
        ApplicationDbContext context,
        IServiceProvider serviceProvider,
        ILogger<DatabaseService> logger,
        IWebHostEnvironment environment)
    {
        _context = context;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _environment = environment;
    }

    public async Task InitializeDatabaseAsync()
    {
        try
        {
            _logger.LogInformation("Starting database initialization...");

            // Ensure database is created
            await CreateDatabaseAsync();

            // Run migrations if needed
            await MigrateDatabaseAsync();

            // Seed initial data
            await SeedDataAsync();

            _logger.LogInformation("Database initialization completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during database initialization.");
            throw;
        }
    }

    public async Task<bool> DatabaseExistsAsync()
    {
        try
        {
            return await _context.Database.CanConnectAsync();
        }
        catch
        {
            return false;
        }
    }

    public async Task CreateDatabaseAsync()
    {
        try
        {
            var created = await _context.Database.EnsureCreatedAsync();
            if (created)
            {
                _logger.LogInformation("Database created successfully.");
            }
            else
            {
                _logger.LogInformation("Database already exists.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating database.");
            throw;
        }
    }

    public async Task MigrateDatabaseAsync()
    {
        try
        {
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("Applying {Count} pending migrations...", pendingMigrations.Count());
                await _context.Database.MigrateAsync();
                _logger.LogInformation("Migrations applied successfully.");
            }
            else
            {
                _logger.LogInformation("No pending migrations found.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying migrations.");
            throw;
        }
    }

    public async Task SeedDataAsync()
    {
        try
        {
            _logger.LogInformation("Starting data seeding...");

            // Seed essential data
            await DbInitializer.InitializeAsync(_serviceProvider);

            // Seed sample data in development
            if (_environment.IsDevelopment())
            {
                _logger.LogInformation("Seeding sample data for development environment...");
                await SampleDataSeeder.SeedSampleDataAsync(_context);
            }

            _logger.LogInformation("Data seeding completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during data seeding.");
            throw;
        }
    }

    public async Task<string> GetDatabaseStatusAsync()
    {
        try
        {
            var canConnect = await DatabaseExistsAsync();
            if (!canConnect)
            {
                return "Database connection failed";
            }

            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            var appliedMigrations = await _context.Database.GetAppliedMigrationsAsync();

            var status = $"Database Status:\n" +
                        $"- Connection: OK\n" +
                        $"- Applied Migrations: {appliedMigrations.Count()}\n" +
                        $"- Pending Migrations: {pendingMigrations.Count()}\n";

            // Check if essential data exists
            var userCount = await _context.Users.CountAsync();
            var roleCount = await _context.Roles.CountAsync();
            var amenityCount = await _context.Amenities.CountAsync();
            var serviceCount = await _context.Services.CountAsync();

            status += $"- Users: {userCount}\n" +
                     $"- Roles: {roleCount}\n" +
                     $"- Amenities: {amenityCount}\n" +
                     $"- Services: {serviceCount}";

            if (_environment.IsDevelopment())
            {
                var motelCount = await _context.Motels.CountAsync();
                var alertCount = await _context.Alerts.CountAsync();
                var commentCount = await _context.Comments.CountAsync();

                status += $"\n- Sample Motels: {motelCount}\n" +
                         $"- Sample Alerts: {alertCount}\n" +
                         $"- Sample Comments: {commentCount}";
            }

            return status;
        }
        catch (Exception ex)
        {
            return $"Error getting database status: {ex.Message}";
        }
    }
}
