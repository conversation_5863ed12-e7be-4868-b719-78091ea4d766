using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ams_web_mvc.Models;

public class Suggestion : SoftDeletableEntity
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public int MotelId { get; set; }

    [Required]
    public string Description { get; set; } = string.Empty;

    [MaxLength(200)]
    public string? AuthorisedBy { get; set; }

    [MaxLength(200)]
    public string? UserOrganization { get; set; }

    [MaxLength(200)]
    public string? UserPositionTitle { get; set; }

    public bool RedCohortFlag { get; set; } = false;

    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual ApplicationUser User { get; set; } = null!;

    [ForeignKey(nameof(MotelId))]
    public virtual Motel Motel { get; set; } = null!;

    public virtual ICollection<SuggestionHistory> SuggestionHistories { get; set; } = new List<SuggestionHistory>();

    // Computed properties
    public bool CanEdit(ApplicationUser user, int editWindowDays = 3)
    {
        if (user.IsAdmin || user.IsManager) return true;
        if (user.Id != UserId) return false;
        
        return CreatedAt >= DateTime.UtcNow.AddDays(-editWindowDays);
    }

    public string CssClass => RedCohortFlag ? "red-cohort-suggestion" : "";
}
