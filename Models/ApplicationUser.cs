using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Models;

public class ApplicationUser : IdentityUser
{
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    public UserRole Role { get; set; } = UserRole.User;

    public string? Jti { get; set; }

    public string? InvitationToken { get; set; }

    public DateTime? InvitationAcceptedAt { get; set; }

    public DateTime? PasswordChangedAt { get; set; }

    public bool PrimaryContact { get; set; } = false;

    public DateTime? DiscardedAt { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Computed properties
    public string FullName => $"{FirstName} {LastName}";

    public bool IsDiscarded => DiscardedAt.HasValue;

    public bool IsAdmin => Role >= UserRole.RestrictedAdmin;

    public bool IsManager => Role >= UserRole.Manager;

    public bool IsPasswordExpired(int expiryDays = 500)
    {
        if (!PasswordChangedAt.HasValue) return true;
        return DateTime.UtcNow > PasswordChangedAt.Value.AddDays(expiryDays);
    }

    public bool CanEdit(BaseEntity entity, int editWindowDays = 3)
    {
        if (IsAdmin || IsManager) return true;
        
        return entity.CreatedAt >= DateTime.UtcNow.AddDays(-editWindowDays);
    }
}
