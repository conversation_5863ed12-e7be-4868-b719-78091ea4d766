using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Models;

public class Comment : BaseEntity
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public int MotelId { get; set; }

    [Required]
    public string Body { get; set; } = string.Empty;

    public CommentStatus ActiveStatus { get; set; } = CommentStatus.ReviewRequired;

    public bool Pinned { get; set; } = false;

    public bool Archived { get; set; } = false;

    [MaxLength(200)]
    public string? UserPositionTitle { get; set; }

    [MaxLength(200)]
    public string? UserOrganizationTitle { get; set; }

    public DateTime? ApprovedAt { get; set; }

    public DateTime? RejectedAt { get; set; }

    [MaxLength(500)]
    public string? RejectionReason { get; set; }

    [MaxLength(1000)]
    public string? RejectionNote { get; set; }

    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual ApplicationUser User { get; set; } = null!;

    [ForeignKey(nameof(MotelId))]
    public virtual Motel Motel { get; set; } = null!;

    public virtual ICollection<CommentHistory> CommentHistories { get; set; } = new List<CommentHistory>();

    // Computed properties
    public bool IsApproved => ActiveStatus == CommentStatus.Approved;

    public bool IsRejected => ActiveStatus == CommentStatus.Rejected;

    public bool RequiresReview => ActiveStatus == CommentStatus.ReviewRequired;

    public string StatusText => ActiveStatus switch
    {
        CommentStatus.Approved => "Approved",
        CommentStatus.Rejected => "Rejected",
        CommentStatus.ReviewRequired => "Pending Review",
        _ => "Unknown"
    };

    public string StatusBadgeClass => ActiveStatus switch
    {
        CommentStatus.Approved => "badge-success",
        CommentStatus.Rejected => "badge-danger",
        CommentStatus.ReviewRequired => "badge-warning",
        _ => "badge-secondary"
    };

    public bool CanEdit(ApplicationUser user, int editWindowDays = 3)
    {
        if (!IsApproved) return false;
        if (user.IsAdmin || user.IsManager) return true;
        if (user.Id != UserId) return false;
        
        return CreatedAt >= DateTime.UtcNow.AddDays(-editWindowDays);
    }

    public void Approve()
    {
        ActiveStatus = CommentStatus.Approved;
        ApprovedAt = DateTime.UtcNow;
        RejectedAt = null;
        RejectionReason = null;
        RejectionNote = null;
    }

    public void Reject(string reason, string? note = null)
    {
        ActiveStatus = CommentStatus.Rejected;
        RejectedAt = DateTime.UtcNow;
        RejectionReason = reason;
        RejectionNote = note;
        ApprovedAt = null;
    }
}
