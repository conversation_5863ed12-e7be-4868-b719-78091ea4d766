using System.ComponentModel.DataAnnotations;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Models;

public class Amenity : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    public AmenityType AmenityType { get; set; }

    [MaxLength(500)]
    public string? HelpText { get; set; }

    // Navigation properties
    public virtual ICollection<AmenityOption> AmenityOptions { get; set; } = new List<AmenityOption>();
    public virtual ICollection<MotelAmenity> MotelAmenities { get; set; } = new List<MotelAmenity>();
}

public class AmenityOption : BaseEntity
{
    [Required]
    public int AmenityId { get; set; }

    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(300)]
    public string? DisplayText { get; set; }

    [MaxLength(50)]
    public string? Icon { get; set; }

    [MaxLength(20)]
    public string? Color { get; set; }

    // Navigation properties
    public virtual Amenity Amenity { get; set; } = null!;
    public virtual ICollection<MotelAmenity> MotelAmenities { get; set; } = new List<MotelAmenity>();
}

public class MotelAmenity : BaseEntity
{
    [Required]
    public int MotelId { get; set; }

    [Required]
    public int AmenityId { get; set; }

    public int? AmenityOptionId { get; set; }

    [MaxLength(500)]
    public string? Note { get; set; }

    // Navigation properties
    public virtual Motel Motel { get; set; } = null!;
    public virtual Amenity Amenity { get; set; } = null!;
    public virtual AmenityOption? AmenityOption { get; set; }
}
