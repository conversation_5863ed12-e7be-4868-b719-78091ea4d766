using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ams_web_mvc.Models;

public enum NotificationEvent
{
    AlertCreated,
    AlertUpdated,
    AlertClosed,
    CommentCreated,
    CommentApproved,
    CommentRejected,
    CautionCreated,
    SuggestionCreated,
    UserInvited,
    AnnouncementCreated
}

public enum ApprovalStatus
{
    Pending,
    Approved,
    Rejected
}

public class Notification : BaseEntity
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public NotificationEvent NotificationEvent { get; set; }

    public ApprovalStatus ApprovalStatus { get; set; } = ApprovalStatus.Approved;

    public bool IsRead { get; set; } = false;

    // Foreign keys for related entities
    public int? MotelId { get; set; }
    public int? AlertId { get; set; }
    public int? CautionId { get; set; }
    public int? SuggestionId { get; set; }
    public int? CommentId { get; set; }
    public int? AnnouncementId { get; set; }

    [MaxLength(100)]
    public string? EventGroupKey { get; set; }

    [MaxLength(500)]
    public string? Title { get; set; }

    public string? Message { get; set; }

    public string? Data { get; set; } // JSON data for additional context

    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual ApplicationUser User { get; set; } = null!;

    [ForeignKey(nameof(MotelId))]
    public virtual Motel? Motel { get; set; }

    [ForeignKey(nameof(AlertId))]
    public virtual Alert? Alert { get; set; }

    [ForeignKey(nameof(CautionId))]
    public virtual Caution? Caution { get; set; }

    [ForeignKey(nameof(SuggestionId))]
    public virtual Suggestion? Suggestion { get; set; }

    [ForeignKey(nameof(CommentId))]
    public virtual Comment? Comment { get; set; }

    // Computed properties
    public bool RequiresApproval => ApprovalStatus == ApprovalStatus.Pending;

    public string StatusText => ApprovalStatus switch
    {
        ApprovalStatus.Pending => "Pending Approval",
        ApprovalStatus.Approved => "Approved",
        ApprovalStatus.Rejected => "Rejected",
        _ => "Unknown"
    };

    public string EventText => NotificationEvent switch
    {
        NotificationEvent.AlertCreated => "New Alert Created",
        NotificationEvent.AlertUpdated => "Alert Updated",
        NotificationEvent.AlertClosed => "Alert Closed",
        NotificationEvent.CommentCreated => "New Comment Added",
        NotificationEvent.CommentApproved => "Comment Approved",
        NotificationEvent.CommentRejected => "Comment Rejected",
        NotificationEvent.CautionCreated => "New Caution Issued",
        NotificationEvent.SuggestionCreated => "New Suggestion Added",
        NotificationEvent.UserInvited => "User Invited",
        NotificationEvent.AnnouncementCreated => "New Announcement",
        _ => "Unknown Event"
    };
}

public class NotificationPreference : BaseEntity
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public NotificationEvent NotificationEvent { get; set; }

    public bool EmailNotificationAllowed { get; set; } = true;

    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual ApplicationUser User { get; set; } = null!;
}
