using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Models;

public class Alert : SoftDeletableEntity
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public int MotelId { get; set; }

    [Required]
    public AlertType AlertType { get; set; }

    [Required]
    [DataType(DataType.Date)]
    public DateTime Date { get; set; }

    [Required]
    [DataType(DataType.Time)]
    public TimeSpan Time { get; set; }

    [MaxLength(500)]
    public string? Location { get; set; }

    [Required]
    public string Description { get; set; } = string.Empty;

    public List<string> Witnesses { get; set; } = new();

    [MaxLength(200)]
    public string? ReportedBy { get; set; }

    public bool CautionIssued { get; set; } = false;

    public DateTime? ClosedAt { get; set; }

    [MaxLength(1000)]
    public string? ClosureNote { get; set; }

    public string? ClosedUserId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual ApplicationUser User { get; set; } = null!;

    [ForeignKey(nameof(MotelId))]
    public virtual Motel Motel { get; set; } = null!;

    [ForeignKey(nameof(ClosedUserId))]
    public virtual ApplicationUser? ClosedByUser { get; set; }

    public virtual ICollection<AlertHistory> AlertHistories { get; set; } = new List<AlertHistory>();

    // Computed properties
    public bool IsOpen => !ClosedAt.HasValue && !IsDiscarded;

    public bool IsClosed => ClosedAt.HasValue && ClosedAt.Value >= DateTime.UtcNow.AddDays(-14);

    public bool IsArchived => IsDiscarded || (ClosedAt.HasValue && ClosedAt.Value < DateTime.UtcNow.AddDays(-14));

    public string StatusText => IsArchived ? "Archived" : (IsClosed ? "Closed" : "Open");

    public string AlertTypeColor => AlertType switch
    {
        AlertType.Category1 => "#760f0f",
        AlertType.Category2 => "#b62525",
        AlertType.Category3 => "#f55c59",
        _ => "#000000"
    };

    public DateTime IncidentDateTime => Date.Date + Time;

    public bool CanEdit(ApplicationUser user, int editWindowDays = 3)
    {
        if (user.IsAdmin || user.IsManager) return true;
        if (user.Id != UserId) return false;
        
        return CreatedAt >= DateTime.UtcNow.AddDays(-editWindowDays);
    }
}
