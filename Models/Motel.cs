using System.ComponentModel.DataAnnotations;
using ams_web_mvc.Models.Enums;

namespace ams_web_mvc.Models;

public class Motel : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [MaxLength(300)]
    public string Street { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string Suburb { get; set; } = string.Empty;

    [Required]
    [MaxLength(10)]
    public string Postcode { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string State { get; set; } = string.Empty;

    public Region Region { get; set; }

    [MaxLength(20)]
    public string? Phone { get; set; }

    [MaxLength(200)]
    [EmailAddress]
    public string? Email { get; set; }

    [MaxLength(500)]
    [Url]
    public string? Website { get; set; }

    public MotelType MotelType { get; set; }

    public Density Density { get; set; }

    public List<Duration> Duration { get; set; } = new();

    public bool Inactive { get; set; } = false;

    // Navigation properties
    public virtual ICollection<Alert> Alerts { get; set; } = new List<Alert>();
    public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
    public virtual ICollection<Caution> Cautions { get; set; } = new List<Caution>();
    public virtual ICollection<Suggestion> Suggestions { get; set; } = new List<Suggestion>();
    public virtual ICollection<MotelAmenity> MotelAmenities { get; set; } = new List<MotelAmenity>();
    public virtual ICollection<MotelService> MotelServices { get; set; } = new List<MotelService>();

    // Computed properties
    public string FullAddress => $"{Street}, {Suburb} {State} {Postcode}";

    public int OpenAlertsCount => Alerts?.Count(a => !a.ClosedAt.HasValue && !a.IsDiscarded) ?? 0;

    public int RecentCommentsCount => Comments?.Count(c => c.CreatedAt >= DateTime.UtcNow.AddDays(-30)) ?? 0;

    public bool HasRecentActivity => 
        OpenAlertsCount > 0 || 
        RecentCommentsCount > 0 ||
        Cautions?.Any(c => !c.IsDiscarded && c.CreatedAt >= DateTime.UtcNow.AddDays(-30)) == true ||
        Suggestions?.Any(s => !s.IsDiscarded && s.CreatedAt >= DateTime.UtcNow.AddDays(-30)) == true;
}
