using System.ComponentModel.DataAnnotations;

namespace ams_web_mvc.Models;

public abstract class BaseEntity
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

public abstract class SoftDeletableEntity : BaseEntity
{
    public DateTime? DiscardedAt { get; set; }
    
    public bool IsDiscarded => DiscardedAt.HasValue;
}
