using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ams_web_mvc.Models;

public abstract class BaseHistoryEntity : BaseEntity
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public string Action { get; set; } = string.Empty; // Created, Updated, Deleted

    public string? Changes { get; set; } // JSON string of changes

    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual ApplicationUser User { get; set; } = null!;
}

public class AlertHistory : BaseHistoryEntity
{
    [Required]
    public int AlertId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(AlertId))]
    public virtual Alert Alert { get; set; } = null!;
}

public class CommentHistory : BaseHistoryEntity
{
    [Required]
    public int CommentId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CommentId))]
    public virtual Comment Comment { get; set; } = null!;
}

public class CautionHistory : BaseHistoryEntity
{
    [Required]
    public int CautionId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CautionId))]
    public virtual Caution Caution { get; set; } = null!;
}

public class SuggestionHistory : BaseHistoryEntity
{
    [Required]
    public int SuggestionId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(SuggestionId))]
    public virtual Suggestion Suggestion { get; set; } = null!;
}

public class MotelChangeLog : BaseHistoryEntity
{
    [Required]
    public int MotelId { get; set; }

    [Required]
    [MaxLength(100)]
    public string FieldName { get; set; } = string.Empty;

    public string? OldValue { get; set; }

    public string? NewValue { get; set; }

    [MaxLength(200)]
    public string? ChangeType { get; set; } // Field, Amenity, Service

    // Navigation properties
    [ForeignKey(nameof(MotelId))]
    public virtual Motel Motel { get; set; } = null!;
}
