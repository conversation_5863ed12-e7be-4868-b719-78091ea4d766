using System.ComponentModel.DataAnnotations;

namespace ams_web_mvc.Models;

public class OrganizationType : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(20)]
    public string? BackgroundColor { get; set; }

    [MaxLength(20)]
    public string? Color { get; set; }

    // Navigation properties
    public virtual ICollection<Service> Services { get; set; } = new List<Service>();
}

public class Service : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [Required]
    public int OrganizationTypeId { get; set; }

    // Navigation properties
    public virtual OrganizationType OrganizationType { get; set; } = null!;
    public virtual ICollection<MotelService> MotelServices { get; set; } = new List<MotelService>();
}

public class MotelService : BaseEntity
{
    [Required]
    public int MotelId { get; set; }

    [Required]
    public int ServiceId { get; set; }

    // Navigation properties
    public virtual Motel Motel { get; set; } = null!;
    public virtual Service Service { get; set; } = null!;
}
